Download Image按钮的下载机制
1. 按钮位置和组件
首页使用的是 KontextDev 组件，Download Image按钮位于结果展示区域。

2. 下载方法详解
index.tsx
components/kontextdev

<Button 
  className="w-full bg-blue-600 hover:bg-blue-700 text-white mt-2" 
  size="lg"
  onClick={async () => {
    try {
      console.log("Starting image download...");
      // 优先使用生成的 URL
      let urlToDownload = generatedImages[activeImageIndex];
      
      if (!urlToDownload) {
        toast.error('No image URL available');
        return;
      }
      
      // 使用代理API下载，避免CORS问题
      const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(urlToDownload)}`;
      
      // 创建隐藏的下载链接
      const link = document.createElement('a');
      link.href = proxyUrl;
      link.download = 'kontext-dev-image.png';
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      
      setTimeout(() => {
        document.body.removeChild(link);
      }, 1000);
      
      toast.success("Image download started");
    } catch (error) {
      // 失败时的回退方案
      window.open(urlToDownload, '_blank');
      toast.info("Please right-click to save image");
    }
  }}
>


3. 下载流程分析
第一步：获取图片URL

从 generatedImages[activeImageIndex] 获取当前激活图片的URL
如果没有可用URL，显示错误提示并返回
第二步：使用代理API

构建代理URL：/api/proxy-image?url=${encodeURIComponent(urlToDownload)}
这样做是为了解决跨域(CORS)下载问题
第三步：创建下载链接

动态创建 <a> 标签
设置 href 为代理URL
设置 download 属性为 'kontext-dev-image.png'
设置 target='_blank' 在新标签页打开
第四步：触发下载

将链接添加到DOM
程序化点击链接触发下载
1秒后移除链接元素

4. 代理API实现
route.ts
app/api/proxy-image


export async function GET(request: NextRequest) {
  // 获取图片URL参数
  const url = request.nextUrl.searchParams.get('url');
  
  // 验证URL有效性
  const imageUrl = decodeURIComponent(url);
  
  // 获取图片内容
  const imageResponse = await fetch(imageUrl, {
    headers: {
      'User-Agent': 'Mozilla/5.0...',
      'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
      'Cache-Control': 'no-cache'
    }
  });
  
  // 设置下载响应头
  headers.set('Content-Disposition', 'attachment; filename="kontext-dev.com.png"');
  
  return new NextResponse(imageBuffer, { headers });
}


5. 错误处理和回退机制
如果代理下载失败，系统会：

记录错误日志
尝试使用原始URL作为回退
在新窗口打开图片
提示用户右键保存图片
6. 关键特性
跨域解决：通过服务端代理API避免CORS限制
用户体验：显示下载进度提示和成功/失败消息
错误恢复：多层回退机制确保用户总能获取图片
文件命名：自动设置有意义的文件名
安全性：URL验证和错误处理
这个下载机制设计得相当完善，既解决了技术问题（CORS），又提供了良好的用户体验。