import { NextResponse } from "next/server";
import Replicate from "replicate";
import { v4 as uuidv4 } from 'uuid';
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";

export const runtime = 'edge';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// 创建S3客户端用于R2上传
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

interface Prediction {
  id: string;
  status: string;
  error?: string;
  output?: string[] | string;
}

// 从URL下载图像并上传到R2，根据用户积分决定是否添加水印
async function uploadImageToR2(imageUrl: string, filename: string, shouldAddWatermark: boolean = false, userUuid?: string): Promise<{url: string, hasWatermark: boolean}> {
  try {
    // 下载图像
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }

    // 获取图像数据并转换为Uint8Array
    const imageBuffer = await response.arrayBuffer();
    let uint8Array = new Uint8Array(imageBuffer);
    let hasWatermark = false;

    // 检查用户积分，决定是否需要水印（文件名标记）
    if (shouldAddWatermark && userUuid) {
      try {
        const { getUserCredits } = await import('@/services/credit');
        const userCredits = await getUserCredits(userUuid);

        if (userCredits.left_credits <= 5) {
          // 用户积分小于等于5，需要水印标记
          // 实际的水印处理将在前端完成，这里只是标记文件名
          console.log(`User has ${userCredits.left_credits} credits (≤5), marking for watermark`);
          hasWatermark = true;
        } else {
          console.log(`User has ${userCredits.left_credits} credits (>5), no watermark needed`);
        }
      } catch (error) {
        console.error('Error checking user credits for watermark:', error);
        // 如果获取积分失败，为了安全起见，标记为需要水印
        console.log('Credit check failed, marking for watermark as safety measure');
        hasWatermark = true;
      }
    }

    // 统一上传原始图片，前端根据用户积分决定是否添加水印
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET || "",
      Key: filename,
      Body: uint8Array,
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);
    console.log('✅ Image uploaded to R2:', filename);

    // 构建URL
    const finalImageUrl = `https://img.kontext-dev.com/${filename}`;

    return {url: finalImageUrl, hasWatermark};
  } catch (error) {
    // 上传失败但不中断整个流程，返回原始URL
    console.log('Falling back to original URL:', imageUrl);
    return {url: imageUrl, hasWatermark: false};
  }
}

// 简化的水印处理 - 直接返回原始图片，让前端处理水印
async function addWatermarkToImage(imageData: Uint8Array): Promise<Uint8Array> {
  try {
    console.log('Server-side watermark processing: Delegating to frontend for better compatibility');

    // 在Edge Runtime环境中，复杂的图像处理可能会有问题
    // 我们将这个任务委托给前端，前端有更好的Canvas API支持
    // 这里我们只是返回原始图片，实际的水印处理将在前端完成

    return imageData;
  } catch (error) {
    console.error('Error in addWatermarkToImage:', error);
    // 如果有任何错误，返回原始图片
    return imageData;
  }
}

// 将生成任务记录保存到数据库
async function saveGenerationRecord(userUuid: string, taskId: string, prompt: string, imageUrl: string, aspectRatio: string): Promise<boolean> {
  try {
    //console.log(`Saving generation record to database, taskId: ${taskId}`);
    
    const supabase = getSupabaseClient();
    
    // 生成一个唯一的callback_id
    const callbackId = `callback_${uuidv4()}`;
    
    // 获取用户信息（nickname和email）
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("nickname, email")
      .eq("uuid", userUuid)
      .single();
      
    if (userError) {
      //console.error('Error getting user data:', userError);
      // 继续执行，不阻止进程
    }
    
    // 准备数据 - 确保字段与数据库表结构完全匹配
    const recordData = {
      user_uuid: userUuid,
      task_id: taskId,
      callback_id: callbackId, // 添加必需的callback_id字段
      prompt: prompt,
      original_image_url: imageUrl,
      // 添加用户信息
      nickname: userData?.nickname || null,
      email: userData?.email || null,
      // 移除不存在的aspect_ratio和model_id字段
      status: 'pending', // 使用与数据库默认值一致的状态
      created_at: new Date().toISOString(),
    };
    
    console.log('Saving record data with user info:', {
      ...recordData,
      prompt: recordData.prompt.substring(0, 30) + '...' // 只记录部分提示词以避免日志过长
    });
    
    // 插入数据
    const { data, error } = await supabase
      .from('4o_generations')
      .insert(recordData)
      .select();
      
    if (error) {
      console.error('Error saving generation record:', error);
      console.error('Error details:', {
        code: error.code,
        message: error.message,
        details: error.details
      });
      return false;
    }
    
    console.log('Generation record saved successfully:', data);
    return true;
  } catch (error) {
    console.error('Error in saveGenerationRecord:', error);
    return false;
  }
}

export async function POST(req: Request) {
  try {
    const { imageUrl: inputImageUrl, prompt, aspectRatio = "match_input_image", outputFormat = "png", addWatermark = true, imageCount = 1, taskId: requestTaskId } = await req.json();

    if (!inputImageUrl || !prompt) {
      return NextResponse.json(
        { error: "Image URL and prompt are required", status: 'failed', taskId: requestTaskId },
        { status: 400 }
      );
    }

    // 获取用户ID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "User authentication required", status: 'failed', taskId: requestTaskId },
        { status: 401 }
      );
    }

    // 确保有有效的任务ID
    const taskId = requestTaskId || `kontextdev_${uuidv4()}`;

    // 不再通过提示词添加水印，而是在后期处理中添加
    let finalPrompt = prompt;

    console.log('Starting generation with params:', {
      task_id: taskId,
      input_image: inputImageUrl,
      prompt: finalPrompt,
      aspect_ratio: aspectRatio,
      output_format: outputFormat,
      add_watermark: addWatermark,
      image_count: imageCount
    });

    // 在生成之前保存记录到数据库
    try {
      await saveGenerationRecord(userUuid, taskId, finalPrompt, inputImageUrl, aspectRatio);
    } catch (dbError) {
      console.error('Failed to save initial record to database:', dbError);
      // 继续处理，不中断流程
    }

    // 创建预测任务
    let prediction;
    try {
      prediction = await replicate.predictions.create({
        model: "black-forest-labs/flux-kontext-dev",
        input: {
          prompt: finalPrompt,
          input_image: inputImageUrl,
          go_fast: true,
          guidance: 2.5,
          aspect_ratio: aspectRatio,
          output_format: outputFormat,
          output_quality: 80,
          num_inference_steps: 30,
          num_outputs: imageCount // 设置生成的图片数量
        }
      }) as Prediction;
    } catch (replicateError) {
      //console.error('API error:', replicateError);
      return NextResponse.json(
        { 
          error: replicateError instanceof Error ? replicateError.message : "Failed to connect to generation service",
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    // 轮询检查预测状态
    let finalPrediction = prediction;
    try {
      while (finalPrediction.status !== "succeeded" && finalPrediction.status !== "failed") {
        await new Promise(resolve => setTimeout(resolve, 1000));
        finalPrediction = await replicate.predictions.get(prediction.id) as Prediction;
      }
    } catch (pollingError) {
      //console.error('轮询状态错误:', pollingError);
      return NextResponse.json(
        { 
          error: "Failed to check generation status", 
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    if (finalPrediction.status === "failed") {
      const errorMessage = finalPrediction.error || "Generation failed";
      //console.error('Generation failed:', errorMessage);
      return NextResponse.json(
        { 
          error: errorMessage, 
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    // 确保输出是有效的 URL
    //console.log('Replicate prediction result:', finalPrediction);
    
    // 处理输出 URL
    let outputUrl;
    
    // 如果输出是字符串，直接使用
    if (typeof finalPrediction.output === 'string') {
      outputUrl = finalPrediction.output;
    } 
    // 如果输出是数组，使用第一个元素
    else if (Array.isArray(finalPrediction.output) && finalPrediction.output.length > 0) {
      outputUrl = finalPrediction.output[0];
    } else {
      return NextResponse.json(
        { 
          error: "No valid output generated", 
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }
    
    // 验证 URL 格式
    if (typeof outputUrl !== 'string' || !outputUrl.startsWith('http')) {
      return NextResponse.json(
        { 
          error: `Invalid output URL: ${outputUrl}`, 
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }
    
    // 从输入图像URL中提取UUID
    let uuid = uuidv4();
    const uuidMatch = inputImageUrl.match(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/);
    if (uuidMatch) {
      uuid = uuidMatch[0];
    }
    
    // 使用指定的命名格式
    const filename = `kontext-dev-com-${uuid}.${outputFormat}`;
    
    try {
      // 将生成的图片上传到 R2 存储，根据用户积分决定是否添加水印
      const uploadResult = await uploadImageToR2(outputUrl, filename, true, userUuid);
      const r2ImageUrl = uploadResult.url;

      console.log(`Image uploaded to R2: ${r2ImageUrl}, hasWatermark: ${uploadResult.hasWatermark}`);

      // 检查用户积分，决定是否在后端更新数据库
      const { getUserCredits } = await import('@/services/credit');
      let shouldUpdateDatabase = true;

      try {
        const userCredits = await getUserCredits(userUuid);
        // 如果用户积分≤5，不在后端更新数据库，让前端水印处理来更新
        if (userCredits.left_credits <= 5) {
          console.log('User has ≤5 credits, skipping backend database update (frontend will handle)');
          shouldUpdateDatabase = false;
        }
      } catch (creditError) {
        console.error('Error checking user credits:', creditError);
        // 如果积分检查失败，为了安全起见，不更新数据库（让前端处理）
        shouldUpdateDatabase = false;
      }

      if (shouldUpdateDatabase) {
        // 直接在后端更新记录状态（仅限积分>5的用户）
        const supabase = getSupabaseClient();
        try {
          // 获取用户信息（确保与初始记录一致）
          const { data: userData, error: userError } = await supabase
            .from("users")
            .select("nickname, email")
            .eq("uuid", userUuid)
            .single();

          if (userError) {
            //console.error('Error getting user data for record update:', userError);
          }

          const updateData = {
            generated_image_url: r2ImageUrl,
            status: 'COMPLETED',
            completed_at: new Date().toISOString(),
            // 确保用户信息一致
            nickname: userData?.nickname || null,
            email: userData?.email || null
          };

          const { error: updateError } = await supabase
            .from("4o_generations")
            .update(updateData)
            .eq("task_id", taskId)
            .eq("user_uuid", userUuid);

          if (updateError) {
            //console.error('Error updating generation record status:', updateError);
          } else {
            //console.log('Generation record status updated successfully with user info');
          }
        } catch (dbError) {
          //console.error('Failed to update generation record status:', dbError);
          // 继续处理，不中断流程
        }
      }

      // 先保存真实的预测结果到数据库（用于后续安全处理）
      const realPredictionResult = {
        status: 'succeeded',
        output: [r2ImageUrl],
        original_output: [outputUrl],
        taskId: taskId
      };

      // 检查用户积分，决定返回什么URL
      let outputUrls = [r2ImageUrl];
      let originalOutputUrls = [outputUrl];
      let needsWatermark = false;

      try {
        const { getUserCredits } = await import('@/services/credit');
        const userCredits = await getUserCredits(userUuid);

        // 如果用户积分≤5，返回真实URL但标记需要前端处理水印
        if (userCredits.left_credits <= 5) {
          console.log('User has ≤5 credits, will return real URLs for frontend watermark processing');
          needsWatermark = true;
          // 保持真实URL，让前端处理水印
          outputUrls = [r2ImageUrl];
          originalOutputUrls = [r2ImageUrl];
        }
      } catch (creditError) {
        console.error('Error checking user credits for URL filtering:', creditError);
        // 如果积分检查失败，为了安全起见，也返回占位符
        needsWatermark = true;
        const placeholderUrl = `placeholder://watermark-required/${taskId}`;
        outputUrls = [placeholderUrl];
        originalOutputUrls = [placeholderUrl];
      }

      // 简化：只在不需要水印时更新数据库
      if (!needsWatermark) {
        try {
          const supabase = getSupabaseClient();
          const { error: updateError } = await supabase
            .from("4o_generations")
            .update({
              generated_image_url: r2ImageUrl,
              status: 'COMPLETED',
              completed_at: new Date().toISOString()
            })
            .eq('task_id', taskId)
            .eq('user_uuid', userUuid);

          if (updateError) {
            console.error('Error updating record for high-credit user:', updateError);
          } else {
            console.log('Record updated for high-credit user');
          }
        } catch (dbError) {
          console.error('Database update error:', dbError);
        }
      }

      // 返回处理后的预测结果
      return NextResponse.json({
        status: 'succeeded',
        output: outputUrls,
        original_output: originalOutputUrls,
        taskId: taskId // 返回任务ID以便客户端可能需要使用
      });
    } catch (uploadError) {
      // 如果上传失败，仍然返回原始URL
      //console.error("Upload to R2 failed, returning original URL:", uploadError);
      
      // 检查用户积分，决定是否在后端更新数据库（回退情况）
      try {
        const { getUserCredits } = await import('@/services/credit');
        let shouldUpdateDatabase = true;

        try {
          const userCredits = await getUserCredits(userUuid);
          // 如果用户积分≤5，不在后端更新数据库，让前端水印处理来更新
          if (userCredits.left_credits <= 5) {
            console.log('User has ≤5 credits, skipping backend database update in fallback (frontend will handle)');
            shouldUpdateDatabase = false;
          }
        } catch (creditError) {
          console.error('Error checking user credits in fallback:', creditError);
          // 如果积分检查失败，为了安全起见，不更新数据库（让前端处理）
          shouldUpdateDatabase = false;
        }

        if (shouldUpdateDatabase) {
          // 即使R2上传失败，也尝试更新数据库记录（仅限积分>5的用户）
          const supabase = getSupabaseClient();

          // 获取用户信息（确保与初始记录一致）
          const { data: userData, error: userError } = await supabase
            .from("users")
            .select("nickname, email")
            .eq("uuid", userUuid)
            .single();

          if (userError) {
           //console.error('Error getting user data for fallback record update:', userError);
          }

          const updateData = {
            generated_image_url: outputUrl, // 使用原始URL
            status: 'COMPLETED',
            completed_at: new Date().toISOString(),
            // 确保用户信息一致
            nickname: userData?.nickname || null,
            email: userData?.email || null
          };

          const { error: updateError } = await supabase
            .from("4o_generations")
            .update(updateData)
            .eq("task_id", taskId)
            .eq("user_uuid", userUuid);

          if (updateError) {
            console.error('Error updating generation record with original URL:', updateError);
          } else {
            console.log('Generation record updated with original URL and user info successfully');
          }
        }
      } catch (dbError) {
        console.error('Failed to update generation record with original URL:', dbError);
      }
      
      // 先保存真实的预测结果到数据库（回退情况）
      const realPredictionResult = {
        status: 'succeeded',
        output: [outputUrl],
        original_output: [outputUrl],
        taskId: taskId
      };

      // 检查用户积分，决定返回什么URL（回退情况）
      let fallbackOutputUrls = [outputUrl];
      let fallbackOriginalUrls = [outputUrl];
      let needsWatermark = false;

      try {
        const { getUserCredits } = await import('@/services/credit');
        const userCredits = await getUserCredits(userUuid);

        // 如果用户积分≤5，返回占位符URL，不暴露真实图片URL
        if (userCredits.left_credits <= 5) {
          console.log('User has ≤5 credits, returning placeholder URLs for security (fallback)');
          needsWatermark = true;
          const placeholderUrl = `placeholder://watermark-required/${taskId}`;
          fallbackOutputUrls = [placeholderUrl];
          fallbackOriginalUrls = [placeholderUrl];
        }
      } catch (creditError) {
        console.error('Error checking user credits for URL filtering (fallback):', creditError);
        // 如果积分检查失败，为了安全起见，也返回占位符
        needsWatermark = true;
        const placeholderUrl = `placeholder://watermark-required/${taskId}`;
        fallbackOutputUrls = [placeholderUrl];
        fallbackOriginalUrls = [placeholderUrl];
      }

      // 保存预测结果到数据库（回退情况）
      try {
        const supabase = getSupabaseClient();
        const { error: saveError } = await supabase
          .from("4o_generations")
          .upsert({
            task_id: taskId,
            user_uuid: userUuid,
            prediction_result: JSON.stringify(realPredictionResult),
            status: needsWatermark ? 'PENDING_WATERMARK' : 'COMPLETED',
            created_at: new Date().toISOString(),
            ...(needsWatermark ? {} : {
              generated_image_url: outputUrl,
              completed_at: new Date().toISOString()
            })
          }, {
            onConflict: 'task_id'
          });

        if (saveError) {
          console.error('Error saving prediction result (fallback):', saveError);
        } else {
          console.log('Prediction result saved to database (fallback)');
        }
      } catch (dbError) {
        console.error('Database save error (fallback):', dbError);
      }

      return NextResponse.json({
        status: 'succeeded',
        output: fallbackOutputUrls,
        original_output: fallbackOriginalUrls,
        upload_error: 'Failed to upload to R2, using original URL',
        taskId: taskId // 返回任务ID以便客户端可能需要使用
      });
    }

  } catch (error) {
    console.error("Generation error:", error);
    // 确保返回taskId，即使是一般性错误
    const taskId = (await req.json()).taskId || null;
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to generate image",
        status: 'failed',
        taskId: taskId
      },
      { status: 500 }
    );
  }
} 
