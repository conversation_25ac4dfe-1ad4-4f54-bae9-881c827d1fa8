<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Localhost Watermark Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .image-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .image-box {
            flex: 1;
            text-align: center;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Localhost Watermark Test</h1>
    <p>This page tests watermark functionality specifically for localhost development with img.kontext-dev.com images.</p>
    
    <div class="test-section">
        <h2>Environment Check</h2>
        <div id="envInfo">
            <strong>Current Environment:</strong><br>
            Hostname: <span id="hostname"></span><br>
            Port: <span id="port"></span><br>
            Protocol: <span id="protocol"></span><br>
            Full URL: <span id="fullUrl"></span>
        </div>
    </div>

    <div class="test-section">
        <h2>Test 1: Proxy API</h2>
        <input type="text" id="testImageUrl" placeholder="Enter img.kontext-dev.com image URL" 
               value="https://img.kontext-dev.com/sample-image.png">
        <button onclick="testProxyAPI()" id="proxyBtn">Test Proxy API</button>
        <div id="proxyResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Cross-Origin Image Loading</h2>
        <button onclick="testDirectImageLoad()" id="directBtn">Test Direct Load</button>
        <button onclick="testProxyImageLoad()" id="proxyLoadBtn">Test Proxy Load</button>
        
        <div class="image-container">
            <div class="image-box">
                <h3>Direct Load</h3>
                <img id="directImage" style="display: none;">
            </div>
            <div class="image-box">
                <h3>Proxy Load</h3>
                <img id="proxyImage" style="display: none;">
            </div>
        </div>
        
        <div id="loadResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Watermark Processing</h2>
        <button onclick="testWatermarkProcessing()" id="watermarkBtn">Test Watermark with R2 Image</button>
        
        <div class="image-container">
            <div class="image-box">
                <h3>Original R2 Image</h3>
                <img id="originalR2Image" style="display: none;">
            </div>
            <div class="image-box">
                <h3>Watermarked Result</h3>
                <img id="watermarkedR2Image" style="display: none;">
            </div>
        </div>
        
        <div id="watermarkResult"></div>
    </div>

    <script>
        // Display environment info
        document.getElementById('hostname').textContent = window.location.hostname;
        document.getElementById('port').textContent = window.location.port;
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('fullUrl').textContent = window.location.href;

        function addResult(containerId, title, content, isError = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<strong>${title}</strong>\n${content}`;
            container.appendChild(div);
        }

        async function testProxyAPI() {
            const btn = document.getElementById('proxyBtn');
            const imageUrl = document.getElementById('testImageUrl').value;
            
            if (!imageUrl) {
                addResult('proxyResult', 'Error', 'Please enter an image URL', true);
                return;
            }

            btn.disabled = true;
            
            try {
                const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
                addResult('proxyResult', 'Testing Proxy API', `Proxy URL: ${proxyUrl}`);
                
                const response = await fetch(proxyUrl);
                
                if (response.ok) {
                    const blob = await response.blob();
                    addResult('proxyResult', 'Proxy API Success', 
                        `Status: ${response.status}\n` +
                        `Content-Type: ${response.headers.get('content-type')}\n` +
                        `Size: ${blob.size} bytes`
                    );
                } else {
                    const errorText = await response.text();
                    addResult('proxyResult', 'Proxy API Failed', 
                        `Status: ${response.status}\n` +
                        `Error: ${errorText}`, true
                    );
                }
            } catch (error) {
                addResult('proxyResult', 'Network Error', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        async function testDirectImageLoad() {
            const imageUrl = document.getElementById('testImageUrl').value;
            const img = document.getElementById('directImage');
            
            addResult('loadResult', 'Testing Direct Image Load', `URL: ${imageUrl}`);
            
            img.onload = function() {
                img.style.display = 'block';
                addResult('loadResult', 'Direct Load Success', 
                    `Dimensions: ${img.width} x ${img.height}`
                );
            };
            
            img.onerror = function() {
                addResult('loadResult', 'Direct Load Failed', 
                    'CORS or network error - this is expected for cross-origin images', true
                );
            };
            
            img.crossOrigin = 'anonymous';
            img.src = imageUrl;
        }

        async function testProxyImageLoad() {
            const imageUrl = document.getElementById('testImageUrl').value;
            const img = document.getElementById('proxyImage');
            const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
            
            addResult('loadResult', 'Testing Proxy Image Load', `Proxy URL: ${proxyUrl}`);
            
            img.onload = function() {
                img.style.display = 'block';
                addResult('loadResult', 'Proxy Load Success', 
                    `Dimensions: ${img.width} x ${img.height}`
                );
            };
            
            img.onerror = function() {
                addResult('loadResult', 'Proxy Load Failed', 
                    'Failed to load image via proxy', true
                );
            };
            
            img.src = proxyUrl;
        }

        async function testWatermarkProcessing() {
            const btn = document.getElementById('watermarkBtn');
            const imageUrl = document.getElementById('testImageUrl').value;
            
            btn.disabled = true;
            
            try {
                addResult('watermarkResult', 'Starting Watermark Test', `Processing: ${imageUrl}`);
                
                // Show original image
                const originalImg = document.getElementById('originalR2Image');
                originalImg.src = imageUrl;
                originalImg.style.display = 'block';
                
                // Process with watermark
                const watermarkedData = await addWatermarkToImage(imageUrl);
                
                // Show watermarked image
                const watermarkedImg = document.getElementById('watermarkedR2Image');
                watermarkedImg.src = watermarkedData;
                watermarkedImg.style.display = 'block';
                
                addResult('watermarkResult', 'Watermark Success', 
                    `Original URL: ${imageUrl}\n` +
                    `Watermarked data length: ${watermarkedData.length} characters`
                );
                
            } catch (error) {
                addResult('watermarkResult', 'Watermark Failed', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        // Copy watermark functions from utils
        async function addWatermarkToImage(imageUrl) {
            return new Promise((resolve, reject) => {
                console.log('🖼️ Loading image for watermark:', imageUrl.substring(0, 50) + '...');
                
                // Check if we need proxy (localhost accessing img.kontext-dev.com)
                const needsProxy = imageUrl.startsWith('https://img.kontext-dev.com/') && 
                                  (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
                
                let finalImageUrl = imageUrl;
                if (needsProxy) {
                    finalImageUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
                    //console.log('🔄 Using proxy for cross-origin image:', finalImageUrl);
                }
                
                const img = new Image();
                
                if (!needsProxy) {
                    img.crossOrigin = 'anonymous';
                }
                
                img.onload = function() {
                    console.log('✅ Image loaded successfully, dimensions:', img.width, 'x', img.height);
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        
                        if (!ctx) {
                            reject(new Error('Failed to get canvas context'));
                            return;
                        }
                        
                        canvas.width = img.width;
                        canvas.height = img.height;
                        
                        ctx.drawImage(img, 0, 0);
                        console.log('✅ Original image drawn to canvas');
                        
                        addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
                        console.log('✅ Watermark added to canvas');
                        
                        const watermarkedData = canvas.toDataURL('image/png');
                        console.log('✅ Canvas converted to data URL, length:', watermarkedData.length);
                        resolve(watermarkedData);
                    } catch (error) {
                        console.error('❌ Error processing image in canvas:', error);
                        reject(error);
                    }
                };
                
                img.onerror = (error) => {
                    console.error('❌ Failed to load image:', error);
                    
                    if (!needsProxy) {
                        console.log('🔄 Trying with proxy API...');
                        const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
                        const img2 = new Image();
                        img2.onload = function() {
                            console.log('✅ Image loaded via proxy');
                            try {
                                const canvas = document.createElement('canvas');
                                const ctx = canvas.getContext('2d');
                                
                                if (!ctx) {
                                    reject(new Error('Failed to get canvas context'));
                                    return;
                                }
                                
                                canvas.width = img2.width;
                                canvas.height = img2.height;
                                ctx.drawImage(img2, 0, 0);
                                addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
                                const watermarkedData = canvas.toDataURL('image/png');
                                resolve(watermarkedData);
                            } catch (canvasError) {
                                console.error('❌ Canvas processing failed:', canvasError);
                                reject(canvasError);
                            }
                        };
                        img2.onerror = () => reject(new Error('Failed to load image even with proxy'));
                        img2.src = proxyUrl;
                    } else {
                        reject(new Error('Failed to load image via proxy'));
                    }
                };
                
                img.src = finalImageUrl;
            });
        }

        function addWatermarkToCanvas(ctx, width, height, text) {
            ctx.save();
            const fontSize = Math.max(width, height) * 0.025;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const baseSpacing = 300;
            const spacingX = Math.max(baseSpacing, width * 0.25);
            const spacingY = Math.max(baseSpacing * 0.7, height * 0.2);
            const angle = -Math.PI / 4;
            const cols = Math.ceil(width / spacingX) + 3;
            const rows = Math.ceil(height / spacingY) + 3;
            const startX = -(cols * spacingX - width) / 2;
            const startY = -(rows * spacingY - height) / 2;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    ctx.save();
                    const x = startX + col * spacingX;
                    const y = startY + row * spacingY;
                    if (x > -spacingX && x < width + spacingX && y > -spacingY && y < height + spacingY) {
                        ctx.translate(x, y);
                        ctx.rotate(angle);
                        ctx.strokeText(text, 0, 0);
                        ctx.fillText(text, 0, 0);
                    }
                    ctx.restore();
                }
            }
            ctx.restore();
        }
    </script>
</body>
</html>
