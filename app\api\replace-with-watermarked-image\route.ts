import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

export const runtime = 'edge';

// 创建S3客户端用于R2上传
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

/**
 * 用带水印的图片替换R2中的原始图片
 */
export async function POST(request: NextRequest) {
  try {
    const { imageData, filename, originalUrl } = await request.json();
    
    if (!imageData || !filename) {
      return NextResponse.json({ error: 'Missing imageData or filename' }, { status: 400 });
    }
    
    console.log('🔄 Replacing original image with watermarked version:', filename);
    
    // 从base64数据中提取图片数据
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');
    
    // 上传到R2，替换原来的文件
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET || "",
      Key: filename,
      Body: imageBuffer,
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);
    
    console.log('✅ Original image replaced with watermarked version:', filename);
    
    return NextResponse.json({ 
      success: true, 
      url: originalUrl,
      message: 'Image replaced with watermarked version successfully' 
    });
    
  } catch (error) {
    console.error('❌ Error replacing image with watermarked version:', error);
    return NextResponse.json({ 
      error: 'Failed to replace image with watermarked version',
      details: (error as Error).message 
    }, { status: 500 });
  }
}
