<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Database Update Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Test Database Update Fix</h1>
    <p>This page helps verify that the database update logic works correctly for different user credit levels.</p>
    
    <div class="test-section">
        <h2>Step 1: Check Current User Credits</h2>
        <button onclick="checkCredits()" id="checkCreditsBtn">Check My Credits</button>
        <div id="creditsResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Simulate Image Generation</h2>
        <p>This simulates the backend logic to check if database updates are skipped for low-credit users.</p>
        <input type="text" id="testTaskId" placeholder="Enter test task ID (e.g., test_12345)" value="test_database_fix">
        <button onclick="simulateGeneration()" id="simulateBtn">Simulate Generation Process</button>
        <div id="simulationResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Test Watermark Upload</h2>
        <p>Test the watermark upload API that should update the database.</p>
        <input type="file" id="fileInput" accept="image/*">
        <button onclick="testWatermarkUpload()" id="uploadBtn">Test Watermark Upload</button>
        <div id="uploadResult"></div>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <div class="result warning">
            <strong>For users with ≤5 credits:</strong>
            1. Backend API should NOT update database
            2. Frontend watermark processing should update database
            3. Final database URL should be watermarked image URL

            <strong>For users with >5 credits:</strong>
            1. Backend API should update database with original image URL
            2. Frontend should display original image (no watermark)
            3. Final database URL should be original image URL
        </div>
    </div>

    <script>
        let currentCredits = null;
        let currentTaskId = '';

        function addResult(containerId, title, content, type = 'success') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${title}</strong>\n${content}`;
            container.appendChild(div);
        }

        async function checkCredits() {
            const btn = document.getElementById('checkCreditsBtn');
            btn.disabled = true;
            
            try {
                const response = await fetch('/api/credits');
                if (response.ok) {
                    const data = await response.json();
                    currentCredits = data.left_credits;
                    
                    const type = data.left_credits <= 5 ? 'warning' : 'success';
                    addResult('creditsResult', 'Credits Check Result', 
                        `Credits: ${data.left_credits}\n` +
                        `Pro Status: ${data.is_pro ? 'Yes' : 'No'}\n` +
                        `Expected Behavior: ${data.left_credits <= 5 ? 'Backend skips DB update, Frontend handles watermark' : 'Backend updates DB with original image'}\n` +
                        `Watermark Required: ${data.left_credits <= 5 ? 'YES' : 'NO'}`,
                        type
                    );
                } else {
                    addResult('creditsResult', 'Credits API Error', await response.text(), 'error');
                }
            } catch (error) {
                addResult('creditsResult', 'Network Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function simulateGeneration() {
            const taskId = document.getElementById('testTaskId').value || `test_${Date.now()}`;
            currentTaskId = taskId;
            
            if (currentCredits === null) {
                addResult('simulationResult', 'Error', 'Please check credits first', 'error');
                return;
            }

            const btn = document.getElementById('simulateBtn');
            btn.disabled = true;

            try {
                addResult('simulationResult', 'Simulation Started', 
                    `Task ID: ${taskId}\n` +
                    `User Credits: ${currentCredits}\n` +
                    `Expected: ${currentCredits <= 5 ? 'Backend will skip database update' : 'Backend will update database'}`
                );

                // This is just a simulation - in real scenario, the backend API would handle this
                if (currentCredits <= 5) {
                    addResult('simulationResult', 'Backend Logic (Simulated)', 
                        '✅ Backend detected user has ≤5 credits\n' +
                        '✅ Backend skipped database update\n' +
                        '✅ Frontend will handle watermark and database update',
                        'success'
                    );
                } else {
                    addResult('simulationResult', 'Backend Logic (Simulated)', 
                        '✅ Backend detected user has >5 credits\n' +
                        '✅ Backend updated database with original image URL\n' +
                        '✅ Frontend will display original image (no watermark)',
                        'success'
                    );
                }

            } catch (error) {
                addResult('simulationResult', 'Simulation Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function testWatermarkUpload() {
            const file = document.getElementById('fileInput').files[0];
            
            if (!file) {
                addResult('uploadResult', 'Error', 'Please select an image first', 'error');
                return;
            }

            if (!currentTaskId) {
                addResult('uploadResult', 'Error', 'Please run simulation first to get a task ID', 'error');
                return;
            }

            const btn = document.getElementById('uploadBtn');
            btn.disabled = true;

            try {
                // Convert to base64
                const reader = new FileReader();
                const base64Promise = new Promise((resolve) => {
                    reader.onload = () => resolve(reader.result);
                });
                reader.readAsDataURL(file);
                const imageData = await base64Promise;

                // Add watermark
                const watermarkedData = await addWatermarkToImage(imageData);

                addResult('uploadResult', 'Watermark Created', 'Watermark added to image successfully');

                // Test upload API
                const uploadResponse = await fetch('/api/upload-watermarked-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageData: watermarkedData,
                        taskId: currentTaskId,
                        originalUrl: 'test-original-url',
                        imageIndex: 0
                    }),
                });

                if (uploadResponse.ok) {
                    const result = await uploadResponse.json();
                    addResult('uploadResult', 'Upload Success', 
                        `✅ Watermarked image uploaded successfully\n` +
                        `✅ Database should be updated with: ${result.watermarkedUrl}\n` +
                        `✅ Task ID: ${currentTaskId}\n` +
                        `\nNext: Check your database to verify the generated_image_url field contains the watermarked URL`,
                        'success'
                    );
                } else {
                    const error = await uploadResponse.text();
                    addResult('uploadResult', 'Upload Failed', error, 'error');
                }

            } catch (error) {
                addResult('uploadResult', 'Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        // Watermark function
        async function addWatermarkToImage(imageData) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    ctx.drawImage(img, 0, 0);
                    addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
                    
                    const watermarkedData = canvas.toDataURL('image/png');
                    resolve(watermarkedData);
                };
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageData;
            });
        }

        function addWatermarkToCanvas(ctx, width, height, text) {
            ctx.save();
            const fontSize = Math.max(width, height) * 0.025;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const baseSpacing = 300;
            const spacingX = Math.max(baseSpacing, width * 0.25);
            const spacingY = Math.max(baseSpacing * 0.7, height * 0.2);
            const angle = -Math.PI / 4;
            const cols = Math.ceil(width / spacingX) + 3;
            const rows = Math.ceil(height / spacingY) + 3;
            const startX = -(cols * spacingX - width) / 2;
            const startY = -(rows * spacingY - height) / 2;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    ctx.save();
                    const x = startX + col * spacingX;
                    const y = startY + row * spacingY;
                    if (x > -spacingX && x < width + spacingX && y > -spacingY && y < height + spacingY) {
                        ctx.translate(x, y);
                        ctx.rotate(angle);
                        ctx.strokeText(text, 0, 0);
                        ctx.fillText(text, 0, 0);
                    }
                    ctx.restore();
                }
            }
            ctx.restore();
        }

        // Auto-check credits on page load
        window.onload = function() {
            checkCredits();
        };
    </script>
</body>
</html>
