<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Credits API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Debug Credits API</h1>
    <p>This page helps debug the credits API and watermark logic.</p>
    
    <button onclick="testCreditsAPI()">Test Credits API</button>
    <button onclick="testWatermarkLogic()">Test Watermark Logic</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        function addResult(title, content, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<strong>${title}</strong>\n${content}`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testCreditsAPI() {
            try {
                addResult('Testing Credits API...', 'Fetching /api/credits');
                
                const response = await fetch('/api/credits');
                
                addResult('Response Status', `Status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('Credits Data', JSON.stringify(data, null, 2));
                    
                    // Test watermark logic
                    const needsWatermark = data.left_credits <= 5;
                    addResult('Watermark Logic', 
                        `Credits: ${data.left_credits}\n` +
                        `Needs Watermark: ${needsWatermark}\n` +
                        `Logic: credits <= 5 = ${data.left_credits} <= 5 = ${needsWatermark}`
                    );
                } else {
                    const errorText = await response.text();
                    addResult('API Error', `Response: ${errorText}`, true);
                }
            } catch (error) {
                addResult('Network Error', error.message, true);
            }
        }

        async function testWatermarkLogic() {
            try {
                addResult('Testing Watermark Logic...', 'Importing watermark functions');
                
                // Test shouldAddWatermark function
                const shouldAdd = await shouldAddWatermark();
                addResult('shouldAddWatermark Result', `Should add watermark: ${shouldAdd}`);
                
            } catch (error) {
                addResult('Watermark Logic Error', error.message, true);
            }
        }

        // Copy the shouldAddWatermark function for testing
        async function shouldAddWatermark() {
            try {
                console.log('🔍 Checking user credits for watermark...');
                const response = await fetch('/api/credits');
                if (response.ok) {
                    const userCredits = await response.json();
                    console.log('💰 User credits:', userCredits.left_credits);
                    const needsWatermark = userCredits.left_credits <= 5;
                    console.log('🎨 Needs watermark:', needsWatermark);
                    return needsWatermark;
                }
                console.warn('⚠️ Credits API response not ok, defaulting to add watermark');
                return true;
            } catch (error) {
                console.error('❌ Error checking user credits:', error);
                return true;
            }
        }

        // Auto-run tests on page load
        window.onload = function() {
            testCreditsAPI();
        };
    </script>
</body>
</html>
