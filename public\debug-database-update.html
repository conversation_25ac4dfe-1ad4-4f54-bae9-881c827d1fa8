<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Database Update</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Debug Database Update Issue</h1>
    <p>This page helps debug why the database is not being updated with watermarked image URLs.</p>
    
    <div class="test-section">
        <h2>Step 1: Check Current User Status</h2>
        <button onclick="checkUserStatus()" id="statusBtn">Check User Status</button>
        <div id="statusResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Test Watermark Function Import</h2>
        <button onclick="testWatermarkImport()" id="importBtn">Test Function Import</button>
        <div id="importResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Simulate Frontend Watermark Process</h2>
        <input type="file" id="fileInput" accept="image/*">
        <input type="text" id="taskIdInput" placeholder="Enter task ID" value="debug_test_123">
        <button onclick="simulateWatermarkProcess()" id="simulateBtn">Simulate Watermark Process</button>
        <div id="simulateResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 4: Check Database Records</h2>
        <input type="text" id="checkTaskId" placeholder="Enter task ID to check">
        <button onclick="checkDatabaseRecord()" id="dbBtn">Check Database Record</button>
        <div id="dbResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 5: Manual Upload Test</h2>
        <button onclick="manualUploadTest()" id="manualBtn">Manual Upload Test</button>
        <div id="manualResult"></div>
    </div>

    <script>
        let currentCredits = null;
        let testImageData = null;

        function addResult(containerId, title, content, type = 'success') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}] ${title}</strong>\n${content}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        async function checkUserStatus() {
            const btn = document.getElementById('statusBtn');
            btn.disabled = true;

            try {
                addResult('statusResult', 'Checking User Credits', 'Fetching user credit information...');

                const response = await fetch('/api/credits');
                if (response.ok) {
                    const data = await response.json();
                    currentCredits = data.left_credits;
                    
                    const type = data.left_credits <= 5 ? 'warning' : 'success';
                    addResult('statusResult', 'User Status', 
                        `Credits: ${data.left_credits}\n` +
                        `Pro Status: ${data.is_pro}\n` +
                        `Should Add Watermark: ${data.left_credits <= 5 ? 'YES' : 'NO'}\n` +
                        `Expected Behavior: ${data.left_credits <= 5 ? 'Frontend should process watermark and update database' : 'Backend should update database with original image'}`,
                        type
                    );
                } else {
                    addResult('statusResult', 'Credits API Error', await response.text(), 'error');
                }
            } catch (error) {
                addResult('statusResult', 'Network Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function testWatermarkImport() {
            const btn = document.getElementById('importBtn');
            btn.disabled = true;

            try {
                addResult('importResult', 'Testing Function Import', 'Testing if watermark functions can be imported...');

                // Test shouldAddWatermark function
                const shouldAdd = await shouldAddWatermark();
                addResult('importResult', 'shouldAddWatermark Result', 
                    `Function imported successfully\n` +
                    `Should add watermark: ${shouldAdd}\n` +
                    `User credits: ${currentCredits || 'Unknown'}`
                );

                // Test processImagesWithWatermark function
                addResult('importResult', 'processImagesWithWatermark', 
                    'Function available: ' + (typeof processImagesWithWatermark === 'function' ? 'YES' : 'NO')
                );

            } catch (error) {
                addResult('importResult', 'Import Test Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function simulateWatermarkProcess() {
            const btn = document.getElementById('simulateBtn');
            const file = document.getElementById('fileInput').files[0];
            const taskId = document.getElementById('taskIdInput').value;

            if (!file) {
                addResult('simulateResult', 'No File Selected', 'Please select an image file', 'error');
                return;
            }

            if (!taskId) {
                addResult('simulateResult', 'No Task ID', 'Please enter a task ID', 'error');
                return;
            }

            btn.disabled = true;

            try {
                addResult('simulateResult', 'Starting Simulation', `Task ID: ${taskId}\nFile: ${file.name}`);

                // Convert to base64
                const reader = new FileReader();
                const base64Promise = new Promise((resolve) => {
                    reader.onload = () => resolve(reader.result);
                });
                reader.readAsDataURL(file);
                const imageData = await base64Promise;
                testImageData = imageData;

                addResult('simulateResult', 'Image Loaded', `Image size: ${imageData.length} characters`);

                // Test shouldAddWatermark
                const needsWatermark = await shouldAddWatermark();
                addResult('simulateResult', 'Watermark Check', `Needs watermark: ${needsWatermark}`);

                if (needsWatermark) {
                    // Add watermark
                    const watermarkedData = await addWatermarkToImage(imageData);
                    addResult('simulateResult', 'Watermark Added', `Watermarked size: ${watermarkedData.length} characters`);

                    // Test upload
                    addResult('simulateResult', 'Testing Upload', 'Calling upload API...');
                    const uploadResponse = await fetch('/api/upload-watermarked-image', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            imageData: watermarkedData,
                            taskId: taskId,
                            originalUrl: 'test-simulation',
                            imageIndex: 0
                        }),
                    });

                    const responseText = await uploadResponse.text();
                    
                    if (uploadResponse.ok) {
                        const result = JSON.parse(responseText);
                        addResult('simulateResult', 'Upload Success', 
                            `✅ Upload successful!\n` +
                            `✅ Watermarked URL: ${result.watermarkedUrl}\n` +
                            `✅ Database should be updated\n` +
                            `✅ Task ID: ${taskId}`
                        );
                        
                        // Store task ID for database check
                        document.getElementById('checkTaskId').value = taskId;
                    } else {
                        addResult('simulateResult', 'Upload Failed', 
                            `❌ Status: ${uploadResponse.status}\n` +
                            `❌ Response: ${responseText}`, 'error'
                        );
                    }
                } else {
                    addResult('simulateResult', 'No Watermark Needed', 'User has sufficient credits, no watermark processing needed');
                }

            } catch (error) {
                addResult('simulateResult', 'Simulation Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function checkDatabaseRecord() {
            const taskId = document.getElementById('checkTaskId').value;
            
            if (!taskId) {
                addResult('dbResult', 'No Task ID', 'Please enter a task ID to check', 'error');
                return;
            }

            addResult('dbResult', 'Database Check', 
                `Task ID: ${taskId}\n` +
                `Note: This is a frontend test. To check the actual database record, you need to:\n` +
                `1. Connect to your database\n` +
                `2. Run: SELECT task_id, generated_image_url, status FROM 4o_generations WHERE task_id = '${taskId}'\n` +
                `3. Check if generated_image_url contains 'watermarked-' prefix`,
                'warning'
            );
        }

        async function manualUploadTest() {
            const btn = document.getElementById('manualBtn');
            
            if (!testImageData) {
                addResult('manualResult', 'No Test Data', 'Please run the simulation test first to generate test data', 'error');
                return;
            }

            btn.disabled = true;

            try {
                addResult('manualResult', 'Manual Upload Test', 'Testing upload with existing test data...');

                const testTaskId = `manual_test_${Date.now()}`;
                const watermarkedData = await addWatermarkToImage(testImageData);

                const uploadResponse = await fetch('/api/upload-watermarked-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageData: watermarkedData,
                        taskId: testTaskId,
                        originalUrl: 'manual-test',
                        imageIndex: 0
                    }),
                });

                const responseText = await uploadResponse.text();
                
                if (uploadResponse.ok) {
                    const result = JSON.parse(responseText);
                    addResult('manualResult', 'Manual Upload Success', 
                        `✅ Manual upload successful!\n` +
                        `✅ Watermarked URL: ${result.watermarkedUrl}\n` +
                        `✅ Task ID: ${testTaskId}\n` +
                        `\nThis proves the upload API works. If the main app doesn't update the database,\n` +
                        `the issue is in the frontend watermark processing logic.`
                    );
                } else {
                    addResult('manualResult', 'Manual Upload Failed', 
                        `❌ Status: ${uploadResponse.status}\n` +
                        `❌ Response: ${responseText}`, 'error'
                    );
                }

            } catch (error) {
                addResult('manualResult', 'Manual Upload Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        // Copy functions from utils/watermark.ts for testing
        async function shouldAddWatermark() {
            try {
                console.log('🔍 Checking user credits for watermark...');
                const response = await fetch('/api/credits');
                if (response.ok) {
                    const userCredits = await response.json();
                    console.log('💰 User credits:', userCredits.left_credits);
                    const needsWatermark = userCredits.left_credits <= 5;
                    console.log('🎨 Needs watermark:', needsWatermark);
                    return needsWatermark;
                }
                console.warn('⚠️ Credits API response not ok, defaulting to add watermark');
                return true;
            } catch (error) {
                console.error('❌ Error checking user credits:', error);
                return true;
            }
        }

        async function addWatermarkToImage(imageData) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    ctx.drawImage(img, 0, 0);
                    
                    // Add watermark
                    ctx.save();
                    const fontSize = Math.max(img.width, img.height) * 0.025;
                    ctx.font = `bold ${fontSize}px Arial`;
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                    ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
                    ctx.lineWidth = 2;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText('kontext-dev.com', img.width / 2, img.height / 2);
                    ctx.strokeText('kontext-dev.com', img.width / 2, img.height / 2);
                    ctx.restore();
                    
                    const watermarkedData = canvas.toDataURL('image/png');
                    resolve(watermarkedData);
                };
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageData;
            });
        }

        // Auto-check user status on page load
        window.onload = function() {
            checkUserStatus();
        };
    </script>
</body>
</html>
