import { NextRequest, NextResponse } from 'next/server';
import { getUserUuid } from '@/services/user';
import Replicate from 'replicate';
import { v4 as uuidv4 } from 'uuid';
import { getSupabaseClient } from '@/models/db';
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";

export const runtime = 'edge';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// S3 client for R2 storage
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

// Save generation record to database
async function saveGenerationRecord(userUuid: string, taskId: string, imageUrl: string): Promise<boolean> {
  try {
    console.log(`Saving background removal generation record to database, taskId: ${taskId}`);

    const supabase = getSupabaseClient();

    // Generate unique callback_id
    const callbackId = `background_removal_callback_${uuidv4()}`;

    // Get user information
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("nickname, email")
      .eq("uuid", userUuid)
      .single();

    if (userError) {
      console.error('Error fetching user data:', userError);
    }

    // Prepare record data
    const recordData = {
      user_uuid: userUuid,
      task_id: taskId,
      callback_id: callbackId,
      prompt: "Remove background from image using AI", // Fixed prompt for background removal
      original_image_url: imageUrl,
      nickname: userData?.nickname || null,
      email: userData?.email || null,
      status: 'pending',
      created_at: new Date().toISOString(),
    };

    console.log('Saving background removal record data:', {
      ...recordData,
      original_image_url: recordData.original_image_url.substring(0, 50) + '...'
    });

    // Insert data
    const { data, error } = await supabase
      .from('4o_generations')
      .insert(recordData)
      .select();

    if (error) {
      console.error('Error saving background removal generation record:', error);
      return false;
    }

    console.log('Background removal generation record saved successfully:', data);
    return true;
  } catch (error) {
    console.error('Error in saveGenerationRecord:', error);
    return false;
  }
}

// Upload image to R2 storage with watermark for low-credit users
async function uploadImageToR2(imageUrl: string, taskId: string, userUuid?: string): Promise<string> {
  try {
    console.log('Downloading image from Replicate...');
    const response = await fetch(imageUrl);
    const imageBuffer = await response.arrayBuffer();
    let uint8Array = new Uint8Array(imageBuffer);

    // Check user credits and add watermark if needed
    if (userUuid) {
      try {
        const { getUserCredits } = await import('@/services/credit');
        const userCredits = await getUserCredits(userUuid);

        if (userCredits.left_credits <= 5) {
          // User has 5 or fewer credits, add watermark
          const watermarkedImageData = await addWatermarkToImage(uint8Array);
          uint8Array = new Uint8Array(watermarkedImageData);
        }
      } catch (error) {
        console.error('Error checking user credits for watermark:', error);
        // If credit check fails, add watermark for safety
        const watermarkedImageData = await addWatermarkToImage(uint8Array);
        uint8Array = new Uint8Array(watermarkedImageData);
      }
    }

    // Generate filename for background removal
    const filename = `background-removed-${taskId}.png`;

    console.log(`Uploading background removed image to R2: ${filename}`);

    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET,
      Key: filename,
      Body: uint8Array,
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);

    // Build new URL
    const newUrl = `https://img.kontext-dev.com/${filename}`;
    console.log('Background removed image uploaded to R2 successfully:', newUrl);

    return newUrl;
  } catch (error) {
    console.error('Error uploading background removed image to R2:', error);
    throw error;
  }
}

// Add watermark to image using Canvas API
async function addWatermarkToImage(imageData: Uint8Array): Promise<Uint8Array> {
  try {
    // Convert image data to base64
    const base64String = btoa(String.fromCharCode(...Array.from(imageData)));
    const imageDataUrl = `data:image/png;base64,${base64String}`;

    // Call watermark processing API
    const watermarkResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/add-watermark`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageData: imageDataUrl,
        watermarkText: 'kontext-dev.com'
      }),
    });

    if (!watermarkResponse.ok) {
      throw new Error('Failed to add watermark');
    }

    const watermarkResult = await watermarkResponse.json();

    // Convert base64 back to Uint8Array
    const watermarkedBase64 = watermarkResult.watermarkedImage.split(',')[1];
    const binaryString = atob(watermarkedBase64);
    const watermarkedUint8Array = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      watermarkedUint8Array[i] = binaryString.charCodeAt(i);
    }

    return watermarkedUint8Array;
  } catch (error) {
    console.error('Error adding watermark:', error);
    // If watermark fails, return original image
    return imageData;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { imageUrl, taskId } = body;

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      );
    }

    // Initialize Replicate client
    const replicate = new Replicate({
      auth: process.env.REPLICATE_API_TOKEN,
    });

    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json(
        { error: 'Replicate API token not configured' },
        { status: 500 }
      );
    }

    console.log('Starting background removal with Replicate for task:', taskId);
    console.log('Input image URL:', imageUrl);

    // Save initial record to database
    try {
      console.log('About to save initial background removal record to database...');
      const saveResult = await saveGenerationRecord(userUuid, taskId, imageUrl);
      console.log('Initial background removal record save result:', saveResult);
    } catch (dbError) {
      console.error('Failed to save initial background removal record to database:', dbError);
      // Continue processing, don't interrupt the flow
    }

    // Call Replicate API for background removal using the exact model from your curl example
    const input = {
      image: imageUrl,
      format: "png",
      reverse: false,
      threshold: 0,
      background_type: "rgba"
    };

    const output = await replicate.run(
      "851-labs/background-remover:a029dff38972b5fda4ec5d75d7d1cd25aeff621d2cf4946a41055d7db66b80bc",
      { input }
    );

    console.log('Replicate output:', output);

    // The output should be a URL to the processed image
    if (!output) {
      return NextResponse.json(
        { error: 'No output received from Replicate' },
        { status: 500 }
      );
    }

    // Extract the URL from the output using the .url() method as per documentation
    let processedImageUrl;
    try {
      // According to the documentation, output should have a .url() method
      if (output && typeof (output as any).url === 'function') {
        processedImageUrl = (output as any).url();
      } else if (typeof output === 'string') {
        processedImageUrl = output;
      } else {
        console.error('Unexpected output format:', output);
        return NextResponse.json(
          { error: 'Unexpected output format from Replicate' },
          { status: 500 }
        );
      }
    } catch (error) {
      console.error('Error extracting URL from output:', error);
      return NextResponse.json(
        { error: 'Failed to extract image URL from result' },
        { status: 500 }
      );
    }

    // Upload generated image to R2 storage
    let r2ImageUrl = processedImageUrl; // Default to original URL
    try {
      r2ImageUrl = await uploadImageToR2(processedImageUrl, taskId, userUuid);
      console.log('Background removed image uploaded to R2 successfully:', r2ImageUrl);
    } catch (uploadError) {
      console.error('Failed to upload background removed image to R2, using original URL:', uploadError);
      // Continue with original URL
    }

    // Update database record with completion status
    try {
      console.log('About to update background removal record in database...');
      const supabase = getSupabaseClient();

      // Get user information for update
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("nickname, email")
        .eq("uuid", userUuid)
        .single();

      if (userError) {
        console.error('Error fetching user data for update:', userError);
      }

      const updateData = {
        generated_image_url: r2ImageUrl,
        status: 'completed',
        completed_at: new Date().toISOString(),
        nickname: userData?.nickname || null,
        email: userData?.email || null
      };

      console.log('Updating background removal record with data:', {
        ...updateData,
        generated_image_url: updateData.generated_image_url.substring(0, 50) + '...',
        taskId,
        userUuid
      });

      const { error: updateError } = await supabase
        .from("4o_generations")
        .update(updateData)
        .eq("task_id", taskId)
        .eq("user_uuid", userUuid);

      if (updateError) {
        console.error('Error updating background removal record:', updateError);
      } else {
        console.log('Background removal record updated successfully in database');
      }
    } catch (dbError) {
      console.error('Failed to update background removal record in database:', dbError);
      // Continue, don't interrupt the response
    }

    // Return the processed image URL
    return NextResponse.json({
      success: true,
      imageUrl: r2ImageUrl,
      taskId: taskId,
      message: 'Background removed successfully'
    });

  } catch (error) {
    console.error('Background removal error:', error);

    // Try to update database record with error status
    try {
      const userUuid = await getUserUuid();
      const body = await request.json();
      const { taskId } = body;

      if (userUuid && taskId) {
        const supabase = getSupabaseClient();
        await supabase
          .from("4o_generations")
          .update({
            status: 'failed',
            completed_at: new Date().toISOString()
          })
          .eq("task_id", taskId)
          .eq("user_uuid", userUuid);
      }
    } catch (dbError) {
      console.error('Failed to update failed background removal record:', dbError);
    }

    return NextResponse.json(
      { error: `Failed to remove background: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

// Example implementation with Remove.bg API (commented out)
/*
async function removeBackgroundWithRemoveBg(imageUrl: string, outputFormat: string) {
  const REMOVE_BG_API_KEY = process.env.REMOVE_BG_API_KEY;
  
  if (!REMOVE_BG_API_KEY) {
    throw new Error('Remove.bg API key not configured');
  }

  const response = await fetch('https://api.remove.bg/v1.0/removebg', {
    method: 'POST',
    headers: {
      'X-Api-Key': REMOVE_BG_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      image_url: imageUrl,
      format: outputFormat,
      size: 'auto'
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.errors?.[0]?.title || 'Background removal failed');
  }

  // The response would be the processed image data
  const imageBuffer = await response.arrayBuffer();
  
  // Upload the processed image to your storage (R2, S3, etc.)
  // and return the URL
  
  return processedImageUrl;
}
*/

// Example implementation with Replicate API (commented out)
/*
async function removeBackgroundWithReplicate(imageUrl: string) {
  const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
  
  if (!REPLICATE_API_TOKEN) {
    throw new Error('Replicate API token not configured');
  }

  const response = await fetch('https://api.replicate.com/v1/predictions', {
    method: 'POST',
    headers: {
      'Authorization': `Token ${REPLICATE_API_TOKEN}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      version: "fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003", // RMBG-1.4 model
      input: {
        image: imageUrl
      }
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to start background removal');
  }

  const prediction = await response.json();
  
  // Poll for completion
  let result = prediction;
  while (result.status === 'starting' || result.status === 'processing') {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const statusResponse = await fetch(`https://api.replicate.com/v1/predictions/${result.id}`, {
      headers: {
        'Authorization': `Token ${REPLICATE_API_TOKEN}`,
      },
    });
    
    result = await statusResponse.json();
  }

  if (result.status === 'failed') {
    throw new Error(result.error || 'Background removal failed');
  }

  return result.output;
}
*/
