<!-- This file should be moved to public/test-watermark.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watermark Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .image-container {
            flex: 1;
            text-align: center;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #fileInput {
            margin: 10px 0;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Watermark Test</h1>
    <p>This page tests the watermark functionality by adding "kontext-dev.com" watermark to uploaded images.</p>
    
    <div>
        <input type="file" id="fileInput" accept="image/*">
        <button onclick="addWatermark()" id="watermarkBtn">Add Watermark</button>
    </div>
    
    <div id="status"></div>
    
    <div class="container">
        <div class="image-container">
            <h3>Original Image</h3>
            <img id="originalImage" style="display: none;">
        </div>
        <div class="image-container">
            <h3>Watermarked Image</h3>
            <img id="watermarkedImage" style="display: none;">
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const originalImage = document.getElementById('originalImage');
        const watermarkedImage = document.getElementById('watermarkedImage');
        const watermarkBtn = document.getElementById('watermarkBtn');
        const status = document.getElementById('status');

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    originalImage.src = e.target.result;
                    originalImage.style.display = 'block';
                    watermarkedImage.style.display = 'none';
                    status.innerHTML = '';
                };
                reader.readAsDataURL(file);
            }
        });

        async function addWatermark() {
            const file = fileInput.files[0];
            if (!file) {
                showStatus('Please select an image first.', 'error');
                return;
            }

            watermarkBtn.disabled = true;
            showStatus('Adding watermark...', 'loading');

            try {
                // Convert file to base64
                const reader = new FileReader();
                const base64Promise = new Promise((resolve) => {
                    reader.onload = () => resolve(reader.result);
                });
                reader.readAsDataURL(file);
                const base64Data = await base64Promise;

                // Call the watermark API
                const response = await fetch('/api/add-watermark', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageData: base64Data,
                        watermarkText: 'kontext-dev.com'
                    }),
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Failed to add watermark');
                }

                const result = await response.json();
                
                // Display the watermarked image
                watermarkedImage.src = result.watermarkedImage;
                watermarkedImage.style.display = 'block';
                
                showStatus('Watermark added successfully!', 'success');
            } catch (error) {
                console.error('Error adding watermark:', error);
                showStatus(`Error: ${error.message}`, 'error');
            } finally {
                watermarkBtn.disabled = false;
            }
        }

        function showStatus(message, type) {
            status.innerHTML = message;
            status.className = `status ${type}`;
        }
    </script>
</body>
</html>
