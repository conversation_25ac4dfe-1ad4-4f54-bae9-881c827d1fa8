<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Buttons Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .image-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .image-box {
            flex: 1;
            text-align: center;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            display: block;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Test Buttons Fix</h1>
    <p>This page tests the View Full Image and Download Image button functionality with different image types.</p>
    
    <div class="test-section">
        <h2>Test 1: HTTP URL Image</h2>
        <div class="image-container">
            <div class="image-box">
                <h3>HTTP URL Image</h3>
                <img id="httpImage" src="https://img.kontext-dev.com/headshot-input-82ecf741-9df3-4ed3-a414-90c07c50588c.jpg" style="max-height: 200px;">
                <button onclick="viewFullImage('https://img.kontext-dev.com/headshot-input-82ecf741-9df3-4ed3-a414-90c07c50588c.jpg')">View Full Image</button>
                <button onclick="downloadImage('https://img.kontext-dev.com/headshot-input-82ecf741-9df3-4ed3-a414-90c07c50588c.jpg')">Download Image</button>
            </div>
        </div>
        <div id="httpResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Base64 Image</h2>
        <input type="file" id="fileInput" accept="image/*" onchange="loadBase64Image()">
        <div class="image-container">
            <div class="image-box">
                <h3>Base64 Image</h3>
                <img id="base64Image" style="display: none; max-height: 200px;">
                <button id="viewBase64Btn" onclick="viewFullImageBase64()" style="display: none;">View Full Image</button>
                <button id="downloadBase64Btn" onclick="downloadImageBase64()" style="display: none;">Download Image</button>
            </div>
        </div>
        <div id="base64Result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Watermarked Image</h2>
        <button onclick="createWatermarkedImage()">Create Watermarked Image</button>
        <div class="image-container">
            <div class="image-box">
                <h3>Watermarked Image</h3>
                <img id="watermarkedImage" style="display: none; max-height: 200px;">
                <button id="viewWatermarkedBtn" onclick="viewFullImageWatermarked()" style="display: none;">View Full Image</button>
                <button id="downloadWatermarkedBtn" onclick="downloadImageWatermarked()" style="display: none;">Download Image</button>
            </div>
        </div>
        <div id="watermarkedResult"></div>
    </div>

    <script>
        let currentBase64 = '';
        let currentWatermarked = '';

        function addResult(containerId, title, content, isError = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<strong>${title}</strong>\n${content}`;
            container.appendChild(div);
        }

        function viewFullImage(url) {
            addResult('httpResult', 'View Full Image (HTTP)', `Opening: ${url}`);
            window.open(url, '_blank');
        }

        function downloadImage(url) {
            addResult('httpResult', 'Download Image (HTTP)', `Downloading via proxy: ${url}`);
            const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(url)}&download=true`;
            const link = document.createElement('a');
            link.href = proxyUrl;
            link.download = `kontext-dev-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function loadBase64Image() {
            const file = document.getElementById('fileInput').files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                currentBase64 = e.target.result;
                const img = document.getElementById('base64Image');
                img.src = currentBase64;
                img.style.display = 'block';
                
                document.getElementById('viewBase64Btn').style.display = 'block';
                document.getElementById('downloadBase64Btn').style.display = 'block';
                
                addResult('base64Result', 'Base64 Image Loaded', 
                    `Data URL length: ${currentBase64.length} characters`
                );
            };
            reader.readAsDataURL(file);
        }

        function viewFullImageBase64() {
            addResult('base64Result', 'View Full Image (Base64)', 'Opening base64 image in new window');
            
            const newWindow = window.open();
            if (newWindow) {
                newWindow.document.write(`
                    <html>
                        <head><title>Base64 Image</title></head>
                        <body style="margin:0; display:flex; justify-content:center; align-items:center; min-height:100vh; background:#f0f0f0;">
                            <img src="${currentBase64}" style="max-width:100%; max-height:100%; object-fit:contain;" />
                        </body>
                    </html>
                `);
                newWindow.document.close();
            }
        }

        function downloadImageBase64() {
            addResult('base64Result', 'Download Image (Base64)', 'Downloading base64 image directly');
            
            const link = document.createElement('a');
            link.href = currentBase64;
            link.download = `kontext-dev-base64-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        async function createWatermarkedImage() {
            if (!currentBase64) {
                addResult('watermarkedResult', 'Error', 'Please load a base64 image first', true);
                return;
            }

            try {
                addResult('watermarkedResult', 'Creating Watermark', 'Processing image...');
                
                currentWatermarked = await addWatermarkToImage(currentBase64);
                
                const img = document.getElementById('watermarkedImage');
                img.src = currentWatermarked;
                img.style.display = 'block';
                
                document.getElementById('viewWatermarkedBtn').style.display = 'block';
                document.getElementById('downloadWatermarkedBtn').style.display = 'block';
                
                addResult('watermarkedResult', 'Watermark Created', 
                    `Watermarked data URL length: ${currentWatermarked.length} characters`
                );
            } catch (error) {
                addResult('watermarkedResult', 'Error', error.message, true);
            }
        }

        function viewFullImageWatermarked() {
            addResult('watermarkedResult', 'View Full Image (Watermarked)', 'Opening watermarked image in new window');
            
            const newWindow = window.open();
            if (newWindow) {
                newWindow.document.write(`
                    <html>
                        <head><title>Watermarked Image</title></head>
                        <body style="margin:0; display:flex; justify-content:center; align-items:center; min-height:100vh; background:#f0f0f0;">
                            <img src="${currentWatermarked}" style="max-width:100%; max-height:100%; object-fit:contain;" />
                        </body>
                    </html>
                `);
                newWindow.document.close();
            }
        }

        function downloadImageWatermarked() {
            addResult('watermarkedResult', 'Download Image (Watermarked)', 'Downloading watermarked image directly');
            
            const link = document.createElement('a');
            link.href = currentWatermarked;
            link.download = `kontext-dev-watermarked-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Watermark function
        async function addWatermarkToImage(imageData) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    ctx.drawImage(img, 0, 0);
                    addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
                    
                    const watermarkedData = canvas.toDataURL('image/png');
                    resolve(watermarkedData);
                };
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageData;
            });
        }

        function addWatermarkToCanvas(ctx, width, height, text) {
            ctx.save();
            const fontSize = Math.max(width, height) * 0.025;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const baseSpacing = 300;
            const spacingX = Math.max(baseSpacing, width * 0.25);
            const spacingY = Math.max(baseSpacing * 0.7, height * 0.2);
            const angle = -Math.PI / 4;
            const cols = Math.ceil(width / spacingX) + 3;
            const rows = Math.ceil(height / spacingY) + 3;
            const startX = -(cols * spacingX - width) / 2;
            const startY = -(rows * spacingY - height) / 2;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    ctx.save();
                    const x = startX + col * spacingX;
                    const y = startY + row * spacingY;
                    if (x > -spacingX && x < width + spacingX && y > -spacingY && y < height + spacingY) {
                        ctx.translate(x, y);
                        ctx.rotate(angle);
                        ctx.strokeText(text, 0, 0);
                        ctx.fillText(text, 0, 0);
                    }
                    ctx.restore();
                }
            }
            ctx.restore();
        }
    </script>
</body>
</html>
