import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import Replicate from 'replicate';
import { v4 as uuidv4 } from 'uuid';
import { getSupabaseClient } from '@/models/db';
import { getUserUuid } from '@/services/user';
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";

export const runtime = 'edge';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// S3 client for R2 storage
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

// Save generation record to database
async function saveGenerationRecord(userUuid: string, taskId: string, imageUrl: string): Promise<boolean> {
  try {
    console.log(`Saving headshot generation record to database, taskId: ${taskId}`);

    const supabase = getSupabaseClient();

    // Generate unique callback_id
    const callbackId = `headshot_callback_${uuidv4()}`;

    // Get user information
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("nickname, email")
      .eq("uuid", userUuid)
      .single();

    if (userError) {
      console.error('Error fetching user data:', userError);
    }

    // Prepare record data
    const recordData = {
      user_uuid: userUuid,
      task_id: taskId,
      callback_id: callbackId,
      prompt: "Generate professional headshot using AI", // Fixed prompt for headshots
      original_image_url: imageUrl,
      nickname: userData?.nickname || null,
      email: userData?.email || null,
      status: 'pending',
      created_at: new Date().toISOString(),
    };

    console.log('Saving headshot record data:', {
      ...recordData,
      original_image_url: recordData.original_image_url.substring(0, 50) + '...'
    });

    // Insert data
    const { data, error } = await supabase
      .from('4o_generations')
      .insert(recordData)
      .select();

    if (error) {
      console.error('Error saving headshot generation record:', error);
      return false;
    }

    console.log('Headshot generation record saved successfully');
    return true;
  } catch (error) {
    console.error('Failed to save headshot generation record:', error);
    return false;
  }
}

// Upload image to R2 storage with watermark for low-credit users
async function uploadImageToR2(imageUrl: string, taskId: string, userUuid?: string): Promise<string> {
  try {
    console.log('Downloading image from Replicate...');
    const response = await fetch(imageUrl);
    const imageBuffer = await response.arrayBuffer();
    let uint8Array = new Uint8Array(imageBuffer);

    // Check user credits and add watermark if needed
    if (userUuid) {
      try {
        const { getUserCredits } = await import('@/services/credit');
        const userCredits = await getUserCredits(userUuid);

        if (userCredits.left_credits <= 5) {
          // User has 5 or fewer credits, add watermark
          const watermarkedImageData = await addWatermarkToImage(uint8Array);
          uint8Array = new Uint8Array(watermarkedImageData);
        }
      } catch (error) {
        console.error('Error checking user credits for watermark:', error);
        // If credit check fails, add watermark for safety
        const watermarkedImageData = await addWatermarkToImage(uint8Array);
        uint8Array = new Uint8Array(watermarkedImageData);
      }
    }

    // Generate filename for headshot
    const filename = `headshot-generated-${taskId}.png`;

    console.log(`Uploading headshot to R2: ${filename}`);

    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET,
      Key: filename,
      Body: uint8Array,
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);

    const r2ImageUrl = `${process.env.STORAGE_DOMAIN}/${filename}`;
    console.log('Headshot uploaded to R2 successfully:', r2ImageUrl);

    return r2ImageUrl;
  } catch (error) {
    console.error('Failed to upload headshot to R2:', error);
    throw error;
  }
}

// Add watermark to image using Canvas API
async function addWatermarkToImage(imageData: Uint8Array): Promise<Uint8Array> {
  try {
    // Convert image data to base64
    const base64String = btoa(String.fromCharCode(...Array.from(imageData)));
    const imageDataUrl = `data:image/png;base64,${base64String}`;

    // Call watermark processing API
    const watermarkResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/add-watermark`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageData: imageDataUrl,
        watermarkText: 'kontext-dev.com'
      }),
    });

    if (!watermarkResponse.ok) {
      throw new Error('Failed to add watermark');
    }

    const watermarkResult = await watermarkResponse.json();

    // Convert base64 back to Uint8Array
    const watermarkedBase64 = watermarkResult.watermarkedImage.split(',')[1];
    const binaryString = atob(watermarkedBase64);
    const watermarkedUint8Array = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      watermarkedUint8Array[i] = binaryString.charCodeAt(i);
    }

    return watermarkedUint8Array;
  } catch (error) {
    console.error('Error adding watermark:', error);
    // If watermark fails, return original image
    return imageData;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user UUID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json({ error: 'Unable to verify user identity' }, { status: 401 });
    }

    const body = await req.json();
    const {
      imageUrl,
      gender = "none",
      background = "neutral",
      aspectRatio = "1:1",
      outputFormat = "png",
      safetyTolerance = 2,
      taskId
    } = body;

    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }

    console.log('Starting headshot generation with Replicate API...');
    console.log('Input parameters:', {
      imageUrl: imageUrl.substring(0, 50) + '...',
      gender,
      background,
      aspectRatio,
      outputFormat,
      safetyTolerance,
      taskId,
      userUuid
    });

    // Save initial record to database
    try {
      await saveGenerationRecord(userUuid, taskId, imageUrl);
    } catch (dbError) {
      console.error('Failed to save initial headshot record to database:', dbError);
      // Continue processing, don't interrupt the flow
    }

    // Prepare input for Replicate API
    const input = {
      input_image: imageUrl,
      gender: gender,
      background: background,
      aspect_ratio: aspectRatio,
      output_format: outputFormat,
      safety_tolerance: safetyTolerance
    };

    // Call Replicate API
    const output = await replicate.run("flux-kontext-apps/professional-headshot", {
      input
    }) as unknown as string;

    console.log('Replicate API response received:', output ? 'Success' : 'Failed');

    if (!output) {
      throw new Error('No output received from Replicate API');
    }

    // Upload generated image to R2 storage
    let r2ImageUrl = output; // Default to original URL
    try {
      r2ImageUrl = await uploadImageToR2(output, taskId, userUuid);
      console.log('Headshot uploaded to R2 successfully:', r2ImageUrl);
    } catch (uploadError) {
      console.error('Failed to upload headshot to R2, using original URL:', uploadError);
      // Continue with original URL
    }

    // Update database record with completion status
    try {
      const supabase = getSupabaseClient();

      // Get user information for update
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("nickname, email")
        .eq("uuid", userUuid)
        .single();

      const updateData = {
        generated_image_url: r2ImageUrl,
        status: 'completed',
        completed_at: new Date().toISOString(),
        nickname: userData?.nickname || null,
        email: userData?.email || null
      };

      const { error: updateError } = await supabase
        .from("4o_generations")
        .update(updateData)
        .eq("task_id", taskId)
        .eq("user_uuid", userUuid);

      if (updateError) {
        console.error('Failed to update headshot generation record status:', updateError);
      } else {
        console.log('Headshot generation record status updated successfully');
      }
    } catch (dbError) {
      console.error('Failed to update headshot generation record status:', dbError);
      // Continue processing, don't interrupt the flow
    }

    // Return the generated image URL
    return NextResponse.json({
      success: true,
      imageUrl: r2ImageUrl,
      taskId: taskId
    });

  } catch (error) {
    console.error('Headshot generation error:', error);

    // Try to update database record with error status
    try {
      const userUuid = await getUserUuid();
      const { taskId } = await req.json();

      if (userUuid && taskId) {
        const supabase = getSupabaseClient();
        await supabase
          .from("4o_generations")
          .update({
            status: 'failed',
            completed_at: new Date().toISOString()
          })
          .eq("task_id", taskId)
          .eq("user_uuid", userUuid);
      }
    } catch (dbError) {
      console.error('Failed to update failed headshot record:', dbError);
    }

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to generate headshot'
    }, { status: 500 });
  }
}
