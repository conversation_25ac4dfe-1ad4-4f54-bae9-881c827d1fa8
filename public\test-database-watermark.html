<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Database Watermark Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .image-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .image-box {
            flex: 1;
            text-align: center;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>Test Database Watermark Integration</h1>
    <p>This page tests the complete watermark integration including database storage.</p>
    
    <div class="test-section">
        <h2>Step 1: Check User Credits</h2>
        <button onclick="checkCredits()" id="checkCreditsBtn">Check My Credits</button>
        <div id="creditsResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Test Watermark Upload API</h2>
        <input type="file" id="fileInput" accept="image/*">
        <input type="text" id="taskIdInput" placeholder="Enter Task ID (e.g., test_12345)" style="margin: 10px; padding: 8px;">
        <button onclick="testWatermarkUpload()" id="uploadBtn">Test Watermark Upload</button>
        
        <div class="image-container">
            <div class="image-box">
                <h3>Original Image</h3>
                <img id="originalImage" style="display: none;">
            </div>
            <div class="image-box">
                <h3>Watermarked Image</h3>
                <img id="watermarkedImage" style="display: none;">
            </div>
        </div>
        
        <div id="uploadResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Verify Database Update</h2>
        <button onclick="checkDatabase()" id="dbBtn">Check Database Record</button>
        <div id="dbResult"></div>
    </div>

    <script>
        let currentTaskId = '';
        let currentCredits = null;

        function addResult(containerId, title, content, isError = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<strong>${title}</strong>\n${content}`;
            container.appendChild(div);
        }

        async function checkCredits() {
            const btn = document.getElementById('checkCreditsBtn');
            btn.disabled = true;
            
            try {
                const response = await fetch('/api/credits');
                if (response.ok) {
                    const data = await response.json();
                    currentCredits = data.left_credits;
                    
                    addResult('creditsResult', 'Credits Check Result', 
                        `Credits: ${data.left_credits}\n` +
                        `Pro Status: ${data.is_pro ? 'Yes' : 'No'}\n` +
                        `Needs Watermark: ${data.left_credits <= 5 ? 'YES' : 'NO'}`
                    );
                } else {
                    addResult('creditsResult', 'Credits API Error', await response.text(), true);
                }
            } catch (error) {
                addResult('creditsResult', 'Network Error', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        async function testWatermarkUpload() {
            const file = document.getElementById('fileInput').files[0];
            const taskId = document.getElementById('taskIdInput').value || `test_${Date.now()}`;
            
            if (!file) {
                addResult('uploadResult', 'Error', 'Please select an image first.', true);
                return;
            }

            currentTaskId = taskId;
            const btn = document.getElementById('uploadBtn');
            btn.disabled = true;

            try {
                // Show original image
                const reader = new FileReader();
                reader.onload = function(e) {
                    const originalImg = document.getElementById('originalImage');
                    originalImg.src = e.target.result;
                    originalImg.style.display = 'block';
                };
                reader.readAsDataURL(file);

                // Convert to base64
                const base64Promise = new Promise((resolve) => {
                    const reader2 = new FileReader();
                    reader2.onload = () => resolve(reader2.result);
                    reader2.readAsDataURL(file);
                });
                const imageData = await base64Promise;

                // Add watermark
                const watermarkedData = await addWatermarkToImage(imageData);
                
                // Show watermarked image
                const watermarkedImg = document.getElementById('watermarkedImage');
                watermarkedImg.src = watermarkedData;
                watermarkedImg.style.display = 'block';

                // Upload to database
                const uploadResponse = await fetch('/api/upload-watermarked-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageData: watermarkedData,
                        taskId: taskId,
                        originalUrl: 'test-original-url'
                    }),
                });

                if (uploadResponse.ok) {
                    const result = await uploadResponse.json();
                    addResult('uploadResult', 'Upload Success', 
                        `Watermarked URL: ${result.watermarkedUrl}\n` +
                        `Task ID: ${taskId}`
                    );
                } else {
                    const error = await uploadResponse.text();
                    addResult('uploadResult', 'Upload Failed', error, true);
                }

            } catch (error) {
                addResult('uploadResult', 'Error', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        async function addWatermarkToImage(imageData) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    // Draw original image
                    ctx.drawImage(img, 0, 0);
                    
                    // Add watermark
                    addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
                    
                    const watermarkedData = canvas.toDataURL('image/png');
                    resolve(watermarkedData);
                };
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageData;
            });
        }

        function addWatermarkToCanvas(ctx, width, height, text) {
            ctx.save();
            const fontSize = Math.max(width, height) * 0.025;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const baseSpacing = 300;
            const spacingX = Math.max(baseSpacing, width * 0.25);
            const spacingY = Math.max(baseSpacing * 0.7, height * 0.2);
            const angle = -Math.PI / 4;
            const cols = Math.ceil(width / spacingX) + 3;
            const rows = Math.ceil(height / spacingY) + 3;
            const startX = -(cols * spacingX - width) / 2;
            const startY = -(rows * spacingY - height) / 2;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    ctx.save();
                    const x = startX + col * spacingX;
                    const y = startY + row * spacingY;
                    if (x > -spacingX && x < width + spacingX && y > -spacingY && y < height + spacingY) {
                        ctx.translate(x, y);
                        ctx.rotate(angle);
                        ctx.strokeText(text, 0, 0);
                        ctx.fillText(text, 0, 0);
                    }
                    ctx.restore();
                }
            }
            ctx.restore();
        }

        async function checkDatabase() {
            if (!currentTaskId) {
                addResult('dbResult', 'Error', 'Please test watermark upload first to get a task ID.', true);
                return;
            }

            const btn = document.getElementById('dbBtn');
            btn.disabled = true;

            try {
                // This would need a custom API to check database records
                // For now, just show that the test was attempted
                addResult('dbResult', 'Database Check', 
                    `Task ID: ${currentTaskId}\n` +
                    `Status: Test completed - check your database manually\n` +
                    `Table: 4o_generations\n` +
                    `Field: generated_image_url should contain the watermarked image URL`
                );
            } catch (error) {
                addResult('dbResult', 'Error', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        // Auto-check credits on page load
        window.onload = function() {
            checkCredits();
        };
    </script>
</body>
</html>
