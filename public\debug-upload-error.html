<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Upload Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Debug Upload Error</h1>
    <p>This page helps debug the watermark image upload error.</p>
    
    <div class="test-section">
        <h2>Test 1: Small Test Image</h2>
        <p>Test with a small generated image to isolate the issue.</p>
        <button onclick="testSmallImage()" id="smallBtn">Test Small Image Upload</button>
        <div id="smallResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Environment Check</h2>
        <p>Check if the upload environment is configured correctly.</p>
        <button onclick="checkEnvironment()" id="envBtn">Check Environment</button>
        <div id="envResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: File Upload</h2>
        <p>Test with a real file upload.</p>
        <input type="file" id="fileInput" accept="image/*">
        <button onclick="testFileUpload()" id="fileBtn">Test File Upload</button>
        <div id="fileResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: API Response Details</h2>
        <p>Get detailed error information from the API.</p>
        <button onclick="testDetailedError()" id="detailBtn">Test Detailed Error</button>
        <div id="detailResult"></div>
    </div>

    <script>
        function addResult(containerId, title, content, isError = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}] ${title}</strong>\n${content}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        async function testSmallImage() {
            const btn = document.getElementById('smallBtn');
            btn.disabled = true;

            try {
                addResult('smallResult', 'Creating Small Test Image', 'Generating a small 100x100 test image...');

                // Create a small test image
                const canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                
                // Fill with a simple pattern
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(0, 0, 100, 100);
                ctx.fillStyle = '#ffffff';
                ctx.font = '12px Arial';
                ctx.fillText('TEST', 30, 50);

                const testImageData = canvas.toDataURL('image/png');
                addResult('smallResult', 'Test Image Created', 
                    `Image data length: ${testImageData.length} characters\n` +
                    `Image starts with: ${testImageData.substring(0, 50)}...`
                );

                // Test upload
                const testTaskId = `debug_small_${Date.now()}`;
                const uploadResponse = await fetch('/api/upload-watermarked-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageData: testImageData,
                        taskId: testTaskId,
                        originalUrl: 'test-small-image',
                        imageIndex: 0
                    }),
                });

                const responseText = await uploadResponse.text();
                
                if (uploadResponse.ok) {
                    const result = JSON.parse(responseText);
                    addResult('smallResult', 'Small Image Upload Success', 
                        `✅ Upload successful!\n` +
                        `✅ Watermarked URL: ${result.watermarkedUrl}\n` +
                        `✅ Task ID: ${testTaskId}`
                    );
                } else {
                    addResult('smallResult', 'Small Image Upload Failed', 
                        `❌ Status: ${uploadResponse.status}\n` +
                        `❌ Response: ${responseText}`, true
                    );
                }

            } catch (error) {
                addResult('smallResult', 'Small Image Test Error', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        async function checkEnvironment() {
            const btn = document.getElementById('envBtn');
            btn.disabled = true;

            try {
                addResult('envResult', 'Checking Environment', 'Testing basic API connectivity...');

                // Test credits API first
                const creditsResponse = await fetch('/api/credits');
                if (creditsResponse.ok) {
                    const creditsData = await creditsResponse.json();
                    addResult('envResult', 'Credits API', 
                        `✅ Credits API working\n` +
                        `✅ User credits: ${creditsData.left_credits}`
                    );
                } else {
                    addResult('envResult', 'Credits API Failed', 
                        `❌ Status: ${creditsResponse.status}`, true
                    );
                }

                // Test a simple POST to our upload API with minimal data
                const testResponse = await fetch('/api/upload-watermarked-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        // Missing required fields to test validation
                    }),
                });

                const testResponseText = await testResponse.text();
                addResult('envResult', 'Upload API Validation', 
                    `Status: ${testResponse.status}\n` +
                    `Response: ${testResponseText}\n` +
                    `Expected: Should return validation error for missing fields`
                );

            } catch (error) {
                addResult('envResult', 'Environment Check Error', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        async function testFileUpload() {
            const btn = document.getElementById('fileBtn');
            const file = document.getElementById('fileInput').files[0];

            if (!file) {
                addResult('fileResult', 'No File Selected', 'Please select a file first', true);
                return;
            }

            btn.disabled = true;

            try {
                addResult('fileResult', 'Processing File', `File: ${file.name}, Size: ${file.size} bytes`);

                // Convert to base64
                const reader = new FileReader();
                const base64Promise = new Promise((resolve, reject) => {
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                });
                reader.readAsDataURL(file);
                const imageData = await base64Promise;

                addResult('fileResult', 'File Converted', 
                    `Base64 length: ${imageData.length} characters\n` +
                    `Data starts with: ${imageData.substring(0, 50)}...`
                );

                // Add watermark
                const watermarkedData = await addWatermarkToImage(imageData);
                addResult('fileResult', 'Watermark Added', 
                    `Watermarked length: ${watermarkedData.length} characters`
                );

                // Test upload
                const testTaskId = `debug_file_${Date.now()}`;
                const uploadResponse = await fetch('/api/upload-watermarked-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageData: watermarkedData,
                        taskId: testTaskId,
                        originalUrl: 'test-file-upload',
                        imageIndex: 0
                    }),
                });

                const responseText = await uploadResponse.text();
                
                if (uploadResponse.ok) {
                    const result = JSON.parse(responseText);
                    addResult('fileResult', 'File Upload Success', 
                        `✅ Upload successful!\n` +
                        `✅ Watermarked URL: ${result.watermarkedUrl}\n` +
                        `✅ Task ID: ${testTaskId}`
                    );
                } else {
                    addResult('fileResult', 'File Upload Failed', 
                        `❌ Status: ${uploadResponse.status}\n` +
                        `❌ Response: ${responseText}`, true
                    );
                }

            } catch (error) {
                addResult('fileResult', 'File Upload Error', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        async function testDetailedError() {
            const btn = document.getElementById('detailBtn');
            btn.disabled = true;

            try {
                addResult('detailResult', 'Testing Detailed Error', 'Sending invalid data to get detailed error info...');

                // Send invalid base64 data to trigger an error
                const testResponse = await fetch('/api/upload-watermarked-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageData: 'data:image/png;base64,invalid_base64_data',
                        taskId: 'debug_error_test',
                        originalUrl: 'test-error',
                        imageIndex: 0
                    }),
                });

                const responseText = await testResponse.text();
                addResult('detailResult', 'Detailed Error Response', 
                    `Status: ${testResponse.status}\n` +
                    `Response: ${responseText}\n` +
                    `This should show detailed error information`
                );

            } catch (error) {
                addResult('detailResult', 'Detailed Error Test Failed', error.message, true);
            } finally {
                btn.disabled = false;
            }
        }

        // Watermark function
        async function addWatermarkToImage(imageData) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    ctx.drawImage(img, 0, 0);
                    
                    // Add simple watermark
                    ctx.save();
                    ctx.font = '20px Arial';
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                    ctx.fillText('kontext-dev.com', 10, 30);
                    ctx.restore();
                    
                    const watermarkedData = canvas.toDataURL('image/png');
                    resolve(watermarkedData);
                };
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageData;
            });
        }
    </script>
</body>
</html>
