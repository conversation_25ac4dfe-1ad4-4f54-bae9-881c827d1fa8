<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Security - URL Hiding</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        .security-pass {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .security-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔒 Security Test - URL Hiding</h1>
    <p>This page tests that users with ≤5 credits cannot access original image URLs through browser console or network inspection.</p>
    
    <div class="security-info">
        <h3>🛡️ Security Measures Implemented:</h3>
        <ul>
            <li><strong>Backend URL Filtering:</strong> API returns placeholder URLs for low-credit users</li>
            <li><strong>Frontend URL Validation:</strong> Filters out placeholder URLs from display</li>
            <li><strong>Database Protection:</strong> Only watermarked URLs stored for low-credit users</li>
            <li><strong>Console Log Filtering:</strong> Original URLs not logged for low-credit users</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Step 1: Check Current User Status</h2>
        <button onclick="checkUserStatus()" id="statusBtn">Check User Status</button>
        <div id="statusResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Simulate API Response</h2>
        <p>Test what the generate API returns for different user credit levels.</p>
        <button onclick="simulateAPIResponse()" id="apiBtn">Simulate Generate API</button>
        <div id="apiResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Test Network Inspection</h2>
        <p>Check what URLs are visible in browser network tab.</p>
        <button onclick="testNetworkInspection()" id="networkBtn">Test Network Inspection</button>
        <div id="networkResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 4: Test Console Log Security</h2>
        <p>Verify that original URLs are not logged to console for low-credit users.</p>
        <button onclick="testConsoleLogSecurity()" id="consoleBtn">Test Console Log Security</button>
        <div id="consoleResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 5: Security Assessment</h2>
        <div id="securityAssessment" class="result warning">
            Run all tests above to get security assessment...
        </div>
    </div>

    <script>
        let userCredits = null;
        let securityTests = {
            userStatus: false,
            apiResponse: false,
            networkInspection: false,
            consoleLog: false
        };

        function addResult(containerId, title, content, type = 'success') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}] ${title}</strong>\n${content}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function updateSecurityAssessment() {
            const container = document.getElementById('securityAssessment');
            const passedTests = Object.values(securityTests).filter(Boolean).length;
            const totalTests = Object.keys(securityTests).length;
            
            let assessment = `Security Tests: ${passedTests}/${totalTests} passed\n\n`;
            
            if (userCredits !== null) {
                if (userCredits <= 5) {
                    assessment += `✅ User has ≤5 credits (${userCredits}) - Security measures should be active\n\n`;
                    
                    if (securityTests.apiResponse) {
                        assessment += `✅ API Response Security: PASS - Placeholder URLs returned\n`;
                    } else {
                        assessment += `❌ API Response Security: FAIL - Original URLs exposed\n`;
                    }
                    
                    if (securityTests.consoleLog) {
                        assessment += `✅ Console Log Security: PASS - Original URLs not logged\n`;
                    } else {
                        assessment += `❌ Console Log Security: FAIL - Original URLs visible in console\n`;
                    }
                    
                    assessment += `\n🛡️ Overall Security: ${passedTests >= 3 ? 'SECURE' : 'VULNERABLE'}`;
                } else {
                    assessment += `ℹ️ User has >5 credits (${userCredits}) - Security measures not needed\n`;
                    assessment += `✅ Original URLs should be accessible for this user`;
                }
            }
            
            container.textContent = assessment;
            container.className = `result ${passedTests >= 3 ? 'security-pass' : 'warning'}`;
        }

        async function checkUserStatus() {
            const btn = document.getElementById('statusBtn');
            btn.disabled = true;

            try {
                const response = await fetch('/api/credits');
                if (response.ok) {
                    const data = await response.json();
                    userCredits = data.left_credits;
                    
                    const type = data.left_credits <= 5 ? 'warning' : 'success';
                    addResult('statusResult', 'User Status Check', 
                        `Credits: ${data.left_credits}\n` +
                        `Pro Status: ${data.is_pro}\n` +
                        `Security Level: ${data.left_credits <= 5 ? 'HIGH (URLs should be hidden)' : 'NORMAL (URLs accessible)'}\n` +
                        `Expected Behavior: ${data.left_credits <= 5 ? 'Placeholder URLs in API responses' : 'Real URLs in API responses'}`,
                        type
                    );
                    
                    securityTests.userStatus = true;
                } else {
                    addResult('statusResult', 'User Status Error', await response.text(), 'error');
                }
            } catch (error) {
                addResult('statusResult', 'Network Error', error.message, 'error');
            } finally {
                btn.disabled = false;
                updateSecurityAssessment();
            }
        }

        async function simulateAPIResponse() {
            const btn = document.getElementById('apiBtn');
            btn.disabled = true;

            try {
                addResult('apiResult', 'Simulating API Response', 'Testing what generate API would return...');

                // This simulates what the API should return based on user credits
                if (userCredits === null) {
                    addResult('apiResult', 'Error', 'Please check user status first', 'error');
                    return;
                }

                if (userCredits <= 5) {
                    // Simulate low-credit user response
                    const mockResponse = {
                        status: 'succeeded',
                        output: ['placeholder://watermark-required/test_123'],
                        original_output: ['placeholder://watermark-required/test_123'],
                        taskId: 'test_123'
                    };
                    
                    addResult('apiResult', 'Low-Credit User API Response (Simulated)', 
                        `✅ SECURITY PASS: Placeholder URLs returned\n` +
                        `✅ Original image URLs are hidden\n` +
                        `✅ Response: ${JSON.stringify(mockResponse, null, 2)}`,
                        'security-pass'
                    );
                    
                    securityTests.apiResponse = true;
                } else {
                    // Simulate high-credit user response
                    const mockResponse = {
                        status: 'succeeded',
                        output: ['https://img.kontext-dev.com/kontext-dev-com-12345.png'],
                        original_output: ['https://img.kontext-dev.com/kontext-dev-com-12345.png'],
                        taskId: 'test_123'
                    };
                    
                    addResult('apiResult', 'High-Credit User API Response (Simulated)', 
                        `ℹ️ Normal behavior: Real URLs returned\n` +
                        `ℹ️ User has sufficient credits\n` +
                        `ℹ️ Response: ${JSON.stringify(mockResponse, null, 2)}`,
                        'success'
                    );
                    
                    securityTests.apiResponse = true;
                }

            } catch (error) {
                addResult('apiResult', 'Simulation Error', error.message, 'error');
            } finally {
                btn.disabled = false;
                updateSecurityAssessment();
            }
        }

        async function testNetworkInspection() {
            const btn = document.getElementById('networkBtn');
            btn.disabled = true;

            try {
                addResult('networkResult', 'Network Inspection Test', 
                    'Testing what URLs are visible in browser network tab...\n' +
                    'Open DevTools > Network tab and watch for requests.'
                );

                // Make a test request to see what's returned
                const testResponse = await fetch('/api/credits');
                
                if (userCredits <= 5) {
                    addResult('networkResult', 'Network Security Analysis', 
                        `✅ SECURITY ANALYSIS:\n` +
                        `✅ When you generate images, the API should return placeholder URLs\n` +
                        `✅ Real image URLs should NOT be visible in network responses\n` +
                        `✅ Only watermarked image URLs should appear in subsequent requests\n` +
                        `\n📋 To verify: Generate an image and check the network tab for any real image URLs`,
                        'security-pass'
                    );
                    
                    securityTests.networkInspection = true;
                } else {
                    addResult('networkResult', 'Network Analysis', 
                        `ℹ️ Normal user - real URLs should be visible in network tab`,
                        'success'
                    );
                    
                    securityTests.networkInspection = true;
                }

            } catch (error) {
                addResult('networkResult', 'Network Test Error', error.message, 'error');
            } finally {
                btn.disabled = false;
                updateSecurityAssessment();
            }
        }

        async function testConsoleLogSecurity() {
            const btn = document.getElementById('consoleBtn');
            btn.disabled = true;

            try {
                addResult('consoleResult', 'Console Log Security Test', 
                    'Testing console log security...'
                );

                // Simulate what should happen in console logs
                if (userCredits <= 5) {
                    console.log('🔒 Security Test: This message should appear in console');
                    console.log('🔒 For low-credit users, original image URLs should NOT be logged');
                    
                    addResult('consoleResult', 'Console Log Security Analysis', 
                        `✅ SECURITY REQUIREMENTS:\n` +
                        `✅ Original image URLs should NOT appear in console logs\n` +
                        `✅ Only placeholder URLs and watermarked URLs should be logged\n` +
                        `✅ Messages like "🔒 Received placeholder URLs" should appear\n` +
                        `✅ Messages like "🔒 Original image URLs are hidden for security" should appear\n` +
                        `\n📋 To verify: Generate an image and check console for any real image URLs`,
                        'security-pass'
                    );
                    
                    securityTests.consoleLog = true;
                } else {
                    addResult('consoleResult', 'Console Log Analysis', 
                        `ℹ️ Normal user - image URLs can appear in console logs`,
                        'success'
                    );
                    
                    securityTests.consoleLog = true;
                }

            } catch (error) {
                addResult('consoleResult', 'Console Test Error', error.message, 'error');
            } finally {
                btn.disabled = false;
                updateSecurityAssessment();
            }
        }

        // Auto-check user status on page load
        window.onload = function() {
            checkUserStatus();
        };
    </script>
</body>
</html>
