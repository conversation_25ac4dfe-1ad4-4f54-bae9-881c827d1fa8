<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Final Fix</h1>
    <p>This page tests the final fix for the watermark and database update issue.</p>
    
    <div class="test-section">
        <h2>Step 1: Check User Status</h2>
        <button onclick="checkUserStatus()" id="statusBtn">Check User Status</button>
        <div id="statusResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Test Secure Watermark API</h2>
        <input type="text" id="testTaskId" placeholder="Enter task ID to test" value="test_secure_api">
        <button onclick="testSecureAPI()" id="secureBtn">Test Secure API</button>
        <div id="secureResult"></div>
    </div>

    <div class="test-section">
        <h2>Step 3: Expected Flow</h2>
        <div class="result warning">
            <strong>Expected Flow for Low-Credit Users:</strong>
            1. Generate API returns placeholder URLs
            2. Generate API saves real prediction result to database
            3. Frontend detects placeholder URLs
            4. Frontend calls secure API with task ID
            5. Secure API returns original URL securely
            6. Frontend processes watermark and uploads
            7. Database gets updated with watermarked URL

            <strong>Key Fix:</strong>
            - Generate API now saves prediction result BEFORE returning placeholder URLs
            - This ensures secure API can find the original URLs
        </div>
    </div>

    <div class="test-section">
        <h2>Step 4: Test Instructions</h2>
        <div class="result warning">
            <strong>To test the complete fix:</strong>
            1. Run the tests above to verify APIs work
            2. Go to the main page (http://localhost:3000/)
            3. Generate an image with a low-credit account (≤5 credits)
            4. Check browser console for the complete flow
            5. Verify database contains watermarked image URL

            <strong>Expected Console Output:</strong>
            🔒 Received placeholder URLs - user has insufficient credits
            🔒 Processing watermark for placeholder URLs
            🔒 Received original URL securely, processing watermark...
            💾 Uploading watermarked image 1 to database...
            ✅ Watermarked image 1 uploaded to database
        </div>
    </div>

    <script>
        let userCredits = null;

        function addResult(containerId, title, content, type = 'success') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}] ${title}</strong>\n${content}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        async function checkUserStatus() {
            const btn = document.getElementById('statusBtn');
            btn.disabled = true;

            try {
                const response = await fetch('/api/credits');
                if (response.ok) {
                    const data = await response.json();
                    userCredits = data.left_credits;
                    
                    const type = data.left_credits <= 5 ? 'warning' : 'success';
                    addResult('statusResult', 'User Status', 
                        `Credits: ${data.left_credits}\n` +
                        `Pro Status: ${data.is_pro}\n` +
                        `Watermark Required: ${data.left_credits <= 5 ? 'YES' : 'NO'}\n` +
                        `Expected Behavior: ${data.left_credits <= 5 ? 'Placeholder URLs → Secure API → Watermark' : 'Normal URLs → No watermark'}`,
                        type
                    );
                } else {
                    addResult('statusResult', 'Credits API Error', await response.text(), 'error');
                }
            } catch (error) {
                addResult('statusResult', 'Network Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function testSecureAPI() {
            const btn = document.getElementById('secureBtn');
            const taskId = document.getElementById('testTaskId').value;

            if (!taskId) {
                addResult('secureResult', 'Error', 'Please enter a task ID', 'error');
                return;
            }

            btn.disabled = true;

            try {
                addResult('secureResult', 'Testing Secure API', `Task ID: ${taskId}`);

                // First, simulate creating a database record
                addResult('secureResult', 'Simulating Database Record', 
                    'In real flow, the generate API would create this record...'
                );

                // Test the secure API
                const response = await fetch('/api/process-watermark-secure', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        taskId: taskId
                    }),
                });

                const responseText = await response.text();
                
                if (response.ok) {
                    const result = JSON.parse(responseText);
                    addResult('secureResult', 'Secure API Success', 
                        `✅ API Response: ${JSON.stringify(result, null, 2)}\n` +
                        `✅ Status: ${response.status}\n` +
                        `✅ This proves the secure API works when database record exists`
                    );
                } else {
                    addResult('secureResult', 'Secure API Response', 
                        `Status: ${response.status}\n` +
                        `Response: ${responseText}\n` +
                        `Note: This might fail if no database record exists for this task ID`,
                        response.status === 404 ? 'warning' : 'error'
                    );
                }

            } catch (error) {
                addResult('secureResult', 'Secure API Error', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        // Auto-check user status on page load
        window.onload = function() {
            checkUserStatus();
        };
    </script>
</body>
</html>
