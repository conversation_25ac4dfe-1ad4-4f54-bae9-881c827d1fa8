# Watermark Feature Implementation

## Overview
This document describes the implementation of the watermark feature for users with 5 or fewer credits.

## Feature Description
- Users with 5 or fewer credits will have watermarks automatically added to their generated images
- The watermark text is "kontext-dev.com" 
- The watermark appears as a diagonal, semi-transparent pattern across the entire image
- Users with more than 5 credits or pro users can choose whether to add watermarks

## Implementation Details

### Backend Changes

#### 1. Watermark API (`/api/add-watermark`)
- New API endpoint that adds watermarks to images using Canvas API
- Uses OffscreenCanvas for server-side image processing
- Creates a diagonal repeating pattern with "kontext-dev.com" text
- Returns base64 encoded watermarked image

#### 2. Modified Image Upload Functions
Updated the following APIs to check user credits and add watermarks:

- `app/api/kontext-dev/generate/route.ts`
- `app/api/headshot-generator/route.ts` 
- `app/api/background-remover/route.ts`

Each `uploadImageToR2` function now:
- Accepts an optional `userUuid` parameter
- Checks user credits using `getUserCredits()`
- Automatically adds watermarks if credits ≤ 5
- Falls back to adding watermarks if credit check fails (for security)

### Frontend Changes

#### 1. KontextDev Component (`components/kontextdev/index.tsx`)
- Modified credit checking logic to enable watermarks for users with ≤ 5 credits
- Updated watermark toggle to prevent users with ≤ 5 credits from disabling watermarks
- Enhanced tooltip text to explain watermark requirements
- Switch control is disabled for low-credit users when watermark is enabled

#### 2. User Experience
- Users with ≤ 5 credits see watermark enabled by default
- Attempting to disable watermark shows upgrade dialog
- Clear messaging about watermark requirements in tooltips

## Watermark Design
The watermark implementation creates:
- Semi-transparent white text with subtle black outline
- 45-degree diagonal rotation
- Repeating grid pattern covering the entire image
- Font size scales with image dimensions (3% of max dimension)
- Spacing between watermarks for readability

## Security Considerations
- Credit check failures default to adding watermarks (fail-safe approach)
- Watermark processing is done server-side to prevent client-side bypass
- All image generation APIs consistently apply the same logic

## Testing
A test page (`test-watermark.html`) is included for manual testing of the watermark functionality.

## Files Modified
- `app/api/add-watermark/route.ts` (new)
- `app/api/kontext-dev/generate/route.ts`
- `app/api/headshot-generator/route.ts`
- `app/api/background-remover/route.ts`
- `components/kontextdev/index.tsx`
- `test-watermark.html` (new)
- `WATERMARK_FEATURE.md` (new)

## Future Enhancements
- Customizable watermark text per deployment
- Different watermark styles for different user tiers
- Watermark positioning options
- Batch watermark processing optimization
