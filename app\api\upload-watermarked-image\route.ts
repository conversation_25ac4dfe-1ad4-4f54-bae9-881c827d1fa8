import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { v4 as uuidv4 } from 'uuid';
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";

export const runtime = 'edge';

const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
});

export async function POST(req: NextRequest) {
  try {
    // 检查用户认证
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { imageData, taskId, originalUrl, imageIndex = 0 } = await req.json();

    if (!imageData || !taskId) {
      return NextResponse.json(
        { error: 'Image data and task ID are required' },
        { status: 400 }
      );
    }

    // 将base64图片数据转换为Buffer
    console.log('Processing image data...');
    console.log('Original imageData length:', imageData.length);
    console.log('ImageData starts with:', imageData.substring(0, 50));

    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    console.log('Base64 data length after processing:', base64Data.length);

    const imageBuffer = Buffer.from(base64Data, 'base64');
    console.log('Image buffer size:', imageBuffer.length, 'bytes');

    // 生成唯一文件名，包含taskId和索引
    const uuid = uuidv4();
    const timestamp = Date.now();
    const filename = `watermarked-${taskId}-${imageIndex}-${timestamp}-${uuid.substring(0, 8)}.png`;

    console.log(`Uploading watermarked image: ${filename}`);
    console.log('Upload details:', {
      bucket: process.env.STORAGE_BUCKET,
      endpoint: process.env.STORAGE_ENDPOINT,
      hasAccessKey: !!process.env.STORAGE_ACCESS_KEY,
      hasSecretKey: !!process.env.STORAGE_SECRET_KEY
    });

    // 上传到R2存储
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET || "",
      Key: filename,
      Body: imageBuffer,
      ContentType: 'image/png',
    });

    console.log('Sending upload command to R2...');
    try {
      const uploadResult = await s3Client.send(uploadCommand);
      console.log('R2 upload successful:', uploadResult);
    } catch (uploadError) {
      console.error('R2 upload failed:', uploadError);
      console.error('Upload error details:', {
        message: uploadError instanceof Error ? uploadError.message : 'Unknown upload error',
        name: uploadError instanceof Error ? uploadError.name : 'Unknown error type',
        code: (uploadError as any)?.code || 'No error code',
        statusCode: (uploadError as any)?.$metadata?.httpStatusCode || 'No status code'
      });
      throw uploadError;
    }

    // 构建新的URL
    const watermarkedUrl = `https://img.kontext-dev.com/${filename}`;
    console.log(`Watermarked image uploaded: ${watermarkedUrl}`);

    // 只在第一张图片时更新数据库中的generated_image_url
    if (imageIndex === 0) {
      const supabase = getSupabaseClient();

      // 获取用户信息
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("nickname, email")
        .eq("uuid", userUuid)
        .single();

      if (userError) {
        console.error('Error getting user data:', userError);
      }

      // 更新数据库记录
      const updateData = {
        generated_image_url: watermarkedUrl,
        status: 'COMPLETED',
        completed_at: new Date().toISOString(),
        nickname: userData?.nickname || null,
        email: userData?.email || null
      };

      const { error: updateError } = await supabase
        .from("4o_generations")
        .update(updateData)
        .eq("task_id", taskId)
        .eq("user_uuid", userUuid);

      if (updateError) {
        console.error('Error updating database with watermarked image:', updateError);
        // 不要因为数据库更新失败而中断流程
      } else {
        console.log('Database updated with watermarked image URL');
      }
    } else {
      console.log(`Skipping database update for image ${imageIndex} (not the primary image)`);
    }

    return NextResponse.json({
      success: true,
      watermarkedUrl: watermarkedUrl,
      originalUrl: originalUrl
    });

  } catch (error) {
    console.error('Error uploading watermarked image:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type'
    });
    return NextResponse.json(
      {
        error: 'Failed to upload watermarked image',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
