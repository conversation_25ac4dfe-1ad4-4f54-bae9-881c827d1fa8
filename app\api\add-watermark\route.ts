import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

export async function POST(req: NextRequest) {
  try {
    const { imageData, watermarkText = 'kontext-dev.com' } = await req.json();

    if (!imageData) {
      return NextResponse.json(
        { error: 'Image data is required' },
        { status: 400 }
      );
    }

    // Since Edge Runtime doesn't support Canvas API, we'll use a different approach
    // We'll create a high-quality SVG that can be converted to PNG by the browser
    const watermarkedImageData = await createHighQualitySVGWatermark(imageData, watermarkText);

    return NextResponse.json({
      success: true,
      watermarkedImage: watermarkedImageData
    });

  } catch (error) {
    console.error('Error adding watermark:', error);
    return NextResponse.json(
      { error: `Failed to add watermark: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

// Create high-quality SVG watermark that can be properly converted to PNG
async function createHighQualitySVGWatermark(imageData: string, watermarkText: string): Promise<string> {
  try {
    // Get image dimensions from the base64 data (this is a simplified approach)
    // For a production system, you might want to decode the image to get exact dimensions
    const defaultWidth = 1024;
    const defaultHeight = 1024;

    // Create a comprehensive SVG with proper watermark pattern
    const svgData = `
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
           width="${defaultWidth}" height="${defaultHeight}" viewBox="0 0 ${defaultWidth} ${defaultHeight}">
        <defs>
          <!-- Watermark pattern that repeats across the image -->
          <pattern id="watermarkPattern" patternUnits="userSpaceOnUse" width="300" height="200">
            <text x="150" y="100"
                  font-family="Arial, sans-serif"
                  font-size="28"
                  font-weight="bold"
                  fill="rgba(255,255,255,0.8)"
                  stroke="rgba(0,0,0,0.6)"
                  stroke-width="1.5"
                  text-anchor="middle"
                  dominant-baseline="middle"
                  transform="rotate(-45 150 100)">
              ${watermarkText}
            </text>
          </pattern>

          <!-- Filter for better text rendering -->
          <filter id="textShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.5)"/>
          </filter>
        </defs>

        <!-- Background image -->
        <image href="${imageData}" width="100%" height="100%" preserveAspectRatio="xMidYMid slice"/>

        <!-- Watermark overlay with multiple instances for better coverage -->
        <rect width="100%" height="100%" fill="url(#watermarkPattern)" opacity="0.6"/>

        <!-- Additional watermark instances for better coverage -->
        <g transform="translate(150, 100)">
          <rect width="100%" height="100%" fill="url(#watermarkPattern)" opacity="0.4"/>
        </g>
      </svg>
    `;

    // Convert SVG to base64
    const svgBase64 = btoa(unescape(encodeURIComponent(svgData)));
    return `data:image/svg+xml;base64,${svgBase64}`;

  } catch (error) {
    console.error('Error in createHighQualitySVGWatermark:', error);
    // Fallback to original image
    return imageData;
  }
}


