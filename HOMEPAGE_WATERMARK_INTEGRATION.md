# 首页水印功能集成文档

## 🎯 功能概述

已成功将水印功能集成到首页的图片生成流程中。现在用户上传图片并通过Replicate API生成图片后，系统会根据用户积分自动决定是否添加水印。

## 🔧 实现架构

### 技术流程
```
用户上传图片 → Replicate API生成 → 后端返回原图URL → 前端检查用户积分 → 根据积分添加水印 → 显示最终结果
```

### 关键决策
- **前端水印处理**：使用Canvas API在客户端添加水印
- **积分检查**：在前端检查用户积分，确保实时性
- **原图保存**：保存原始图片URL，支持水印切换

## 📁 修改的文件

### 1. 新增文件
- `utils/watermark.ts` - 水印处理工具函数
- `public/test-integration.html` - 集成测试页面
- `HOMEPAGE_WATERMARK_INTEGRATION.md` - 本文档

### 2. 修改的文件
- `components/kontextdev/index.tsx` - 主要组件，集成水印逻辑
- `app/api/kontext-dev/generate/route.ts` - 后端API，移除服务端水印

## 🎨 核心功能实现

### 1. 水印工具函数 (`utils/watermark.ts`)

#### `addWatermarkToImage(imageUrl: string): Promise<string>`
- 使用Canvas API为单张图片添加水印
- 支持跨域图片处理
- 返回base64格式的水印图片

#### `shouldAddWatermark(): Promise<boolean>`
- 检查用户积分
- 积分 ≤ 5 返回 true（需要水印）
- 积分 > 5 返回 false（不需要水印）

#### `processImagesWithWatermark(imageUrls: string[]): Promise<string[]>`
- 批量处理图片数组
- 根据用户积分决定是否添加水印
- 支持多图片并行处理

### 2. 前端集成 (`components/kontextdev/index.tsx`)

#### 图片生成完成后的处理
```typescript
// 根据用户积分处理水印
const processedImages = await processImagesWithWatermark(validOutputUrls);
setGeneratedImages(processedImages);
```

#### 水印切换功能
```typescript
const handleWatermarkToggle = async (checked: boolean) => {
  // 积分检查和权限控制
  if (userCredits !== null && userCredits <= 5 && !checked) {
    setShowUpgradeDialog(true);
    return;
  }
  
  // 重新处理已生成的图片
  if (originalImageUrls.length > 0) {
    const processedImages = checked 
      ? await processImagesWithWatermark(originalImageUrls)
      : originalImageUrls;
    setGeneratedImages(processedImages);
  }
};
```

### 3. 后端修改 (`app/api/kontext-dev/generate/route.ts`)

移除服务端水印处理：
```typescript
// 修改前：根据addWatermark参数添加水印
const r2ImageUrl = await uploadImageToR2(outputUrl, filename, addWatermark, userUuid);

// 修改后：不在服务端添加水印
const r2ImageUrl = await uploadImageToR2(outputUrl, filename, false, userUuid);
```

## 🎯 用户体验流程

### 积分 ≤ 5 的用户
1. 上传图片并生成
2. 系统自动检测积分不足
3. 自动为生成的图片添加水印
4. 显示带水印的图片
5. 水印切换被禁用，显示升级提示

### 积分 > 5 的用户
1. 上传图片并生成
2. 系统检测积分充足
3. 显示无水印的原图
4. 可以自由切换是否添加水印
5. 实时重新处理图片

## 🔒 权限控制

### 水印切换权限
- **积分 ≤ 5**：强制启用水印，无法关闭
- **积分 > 5 且非Pro**：可以启用水印，关闭时显示升级提示
- **Pro用户**：完全自由控制水印开关

### 安全考虑
- 前端积分检查 + 后端验证双重保障
- 积分获取失败时默认添加水印（安全优先）
- 水印处理失败时返回原图（用户体验优先）

## 🎨 水印设计规格

### 视觉效果
- **文字内容**：kontext-dev.com
- **字体**：Arial Bold
- **颜色**：白色70%透明度 + 黑色50%描边
- **角度**：-45度斜向
- **密度**：适中稀疏，不影响图片观看

### 自适应特性
- **字体大小**：图片尺寸的2.5%
- **间距**：宽度25%，高度20%（最小300px×210px）
- **网格布局**：居中对齐，完整覆盖

## 🧪 测试方案

### 1. 单元测试
访问 `http://localhost:3000/test-watermark.html` 测试水印API

### 2. 集成测试
访问 `http://localhost:3000/test-integration.html` 测试完整流程

### 3. 用户场景测试
- 低积分用户（≤5积分）
- 高积分用户（>5积分）
- Pro用户
- 积分获取失败场景

## 🚀 部署注意事项

### 环境变量
确保以下环境变量正确配置：
- `NEXT_PUBLIC_APP_URL` - 用于API调用
- `REPLICATE_API_TOKEN` - Replicate API访问
- 其他现有的存储和数据库配置

### 性能考虑
- Canvas处理在客户端进行，不增加服务器负载
- 大图片可能消耗较多客户端内存
- 建议对超大图片进行尺寸限制

### 兼容性
- 支持所有现代浏览器
- 需要JavaScript支持
- Canvas API兼容性良好

## 📈 后续优化建议

1. **批量处理优化**：对多图片生成进行性能优化
2. **缓存机制**：缓存已处理的水印图片
3. **水印样式**：支持更多水印样式选项
4. **服务端备份**：关键场景的服务端水印备份
5. **监控统计**：添加水印使用情况统计

## 🎉 总结

水印功能已成功集成到首页，实现了：
- ✅ 根据用户积分自动添加水印
- ✅ 实时水印切换功能
- ✅ 良好的用户体验和权限控制
- ✅ 高性能的前端处理方案
- ✅ 完整的测试和文档支持

用户现在可以在首页正常使用图片生成功能，系统会根据其积分状态自动处理水印，既保护了平台利益，又提供了良好的用户体验。
