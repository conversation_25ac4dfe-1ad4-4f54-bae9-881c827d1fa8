<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Integration Test - Watermark Based on Credits</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .image-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .image-box {
            flex: 1;
            text-align: center;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .credits-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Integration Test - Watermark Based on Credits</h1>
    <p>This page tests the complete watermark integration: checking user credits and applying watermarks accordingly.</p>
    
    <div class="test-section">
        <h2>Step 1: Check User Credits</h2>
        <button onclick="checkCredits()" id="checkCreditsBtn">Check My Credits</button>
        <div id="creditsInfo" class="credits-info" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Test Watermark Logic</h2>
        <input type="file" id="fileInput" accept="image/*">
        <button onclick="testWatermarkLogic()" id="testBtn">Test Watermark Logic</button>
        <div id="status"></div>
        
        <div class="image-container">
            <div class="image-box">
                <h3>Original Image</h3>
                <img id="originalImage" style="display: none;">
            </div>
            <div class="image-box">
                <h3>Processed Image (Based on Credits)</h3>
                <img id="processedImage" style="display: none;">
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Step 3: Test Credit Scenarios</h2>
        <p>Simulate different credit scenarios:</p>
        <button onclick="simulateCredits(3)">Simulate 3 Credits (Should Add Watermark)</button>
        <button onclick="simulateCredits(10)">Simulate 10 Credits (Should NOT Add Watermark)</button>
        <div id="simulationResult"></div>
    </div>

    <script>
        let currentCredits = null;
        let testImageData = null;

        async function checkCredits() {
            const btn = document.getElementById('checkCreditsBtn');
            const info = document.getElementById('creditsInfo');
            
            btn.disabled = true;
            btn.textContent = 'Checking...';
            
            try {
                const response = await fetch('/api/credits');
                if (response.ok) {
                    const data = await response.json();
                    currentCredits = data.left_credits;
                    
                    info.innerHTML = `
                        <strong>Your Current Credits: ${data.left_credits}</strong><br>
                        Pro Status: ${data.is_pro ? 'Yes' : 'No'}<br>
                        Watermark Required: ${data.left_credits <= 5 ? 'Yes' : 'No'}
                    `;
                    info.style.display = 'block';
                } else {
                    throw new Error('Failed to fetch credits');
                }
            } catch (error) {
                info.innerHTML = `<strong>Error:</strong> ${error.message}`;
                info.style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.textContent = 'Check My Credits';
            }
        }

        async function testWatermarkLogic() {
            const file = document.getElementById('fileInput').files[0];
            if (!file) {
                showStatus('Please select an image first.', 'error');
                return;
            }

            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            showStatus('Testing watermark logic...', 'loading');

            try {
                // Convert file to base64
                const reader = new FileReader();
                const base64Promise = new Promise((resolve) => {
                    reader.onload = () => resolve(reader.result);
                });
                reader.readAsDataURL(file);
                testImageData = await base64Promise;

                // Show original image
                const originalImg = document.getElementById('originalImage');
                originalImg.src = testImageData;
                originalImg.style.display = 'block';

                // Import watermark utility (simulate)
                const processedImageData = await processImageBasedOnCredits(testImageData);
                
                // Show processed image
                const processedImg = document.getElementById('processedImage');
                processedImg.src = processedImageData;
                processedImg.style.display = 'block';
                
                showStatus('Test completed successfully!', 'success');
            } catch (error) {
                console.error('Error testing watermark logic:', error);
                showStatus(`Error: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function processImageBasedOnCredits(imageData) {
            // Check if user needs watermark
            const response = await fetch('/api/credits');
            const credits = await response.json();
            
            if (credits.left_credits <= 5) {
                // Add watermark
                return await addWatermarkToImage(imageData);
            } else {
                // Return original image
                return imageData;
            }
        }

        async function addWatermarkToImage(imageData) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    // Draw original image
                    ctx.drawImage(img, 0, 0);
                    
                    // Add watermark
                    addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
                    
                    const watermarkedData = canvas.toDataURL('image/png');
                    resolve(watermarkedData);
                };
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageData;
            });
        }

        function addWatermarkToCanvas(ctx, width, height, text) {
            ctx.save();
            const fontSize = Math.max(width, height) * 0.025;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const baseSpacing = 300;
            const spacingX = Math.max(baseSpacing, width * 0.25);
            const spacingY = Math.max(baseSpacing * 0.7, height * 0.2);
            const angle = -Math.PI / 4;
            const cols = Math.ceil(width / spacingX) + 3;
            const rows = Math.ceil(height / spacingY) + 3;
            const startX = -(cols * spacingX - width) / 2;
            const startY = -(rows * spacingY - height) / 2;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    ctx.save();
                    const x = startX + col * spacingX;
                    const y = startY + row * spacingY;
                    if (x > -spacingX && x < width + spacingX && y > -spacingY && y < height + spacingY) {
                        ctx.translate(x, y);
                        ctx.rotate(angle);
                        ctx.strokeText(text, 0, 0);
                        ctx.fillText(text, 0, 0);
                    }
                    ctx.restore();
                }
            }
            ctx.restore();
        }

        async function simulateCredits(credits) {
            const result = document.getElementById('simulationResult');
            result.innerHTML = `
                <div class="status ${credits <= 5 ? 'error' : 'success'}">
                    <strong>Simulation:</strong> User has ${credits} credits<br>
                    <strong>Watermark Required:</strong> ${credits <= 5 ? 'YES' : 'NO'}<br>
                    <strong>Reason:</strong> ${credits <= 5 ? 'Credits ≤ 5, watermark will be added' : 'Credits > 5, no watermark needed'}
                </div>
            `;
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.innerHTML = message;
            status.className = `status ${type}`;
        }

        // Auto-check credits on page load
        window.onload = function() {
            checkCredits();
        };
    </script>
</body>
</html>
