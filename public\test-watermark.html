<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watermark Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .image-container {
            flex: 1;
            text-align: center;
        }
        img {
            max-width: 100%;
            max-height: 600px;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            object-fit: contain;
        }
        #originalImage {
            max-width: 100%;
            max-height: 600px;
        }
        #watermarkedImage {
            max-width: 100%;
            max-height: 600px;
            height: auto;
            border: 2px solid #007bff;
            object-fit: contain;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #fileInput {
            margin: 10px 0;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Watermark Test</h1>
    <p>This page tests the watermark functionality by adding "kontext-dev.com" watermark to uploaded images.</p>
    
    <div>
        <input type="file" id="fileInput" accept="image/*">
        <button onclick="addWatermark()" id="watermarkBtn">Add Watermark</button>
        <button onclick="downloadWatermarkedImage()" id="downloadBtn" style="display: none;">Download Watermarked Image</button>
    </div>
    
    <div id="status"></div>
    
    <div class="container">
        <div class="image-container">
            <h3>Original Image</h3>
            <img id="originalImage" style="display: none;">
        </div>
        <div class="image-container">
            <h3>Watermarked Image</h3>
            <img id="watermarkedImage" style="display: none;">
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const originalImage = document.getElementById('originalImage');
        const watermarkedImage = document.getElementById('watermarkedImage');
        const watermarkBtn = document.getElementById('watermarkBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const status = document.getElementById('status');

        let currentWatermarkedImageData = null;

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    originalImage.src = e.target.result;
                    originalImage.style.display = 'block';
                    watermarkedImage.style.display = 'none';
                    downloadBtn.style.display = 'none';
                    currentWatermarkedImageData = null;
                    status.innerHTML = '';
                };
                reader.readAsDataURL(file);
            }
        });

        async function addWatermark() {
            const file = fileInput.files[0];
            if (!file) {
                showStatus('Please select an image first.', 'error');
                return;
            }

            watermarkBtn.disabled = true;
            showStatus('Adding watermark...', 'loading');

            try {
                // Convert file to base64
                const reader = new FileReader();
                const base64Promise = new Promise((resolve) => {
                    reader.onload = () => resolve(reader.result);
                });
                reader.readAsDataURL(file);
                const base64Data = await base64Promise;

                // Create watermark using Canvas (client-side)
                const watermarkedImageData = await addWatermarkWithCanvas(base64Data);

                // Display the watermarked image
                watermarkedImage.src = watermarkedImageData;
                watermarkedImage.style.display = 'block';

                // Store the watermarked image data for download
                currentWatermarkedImageData = watermarkedImageData;
                downloadBtn.style.display = 'inline-block';

                showStatus('Watermark added successfully!', 'success');
            } catch (error) {
                console.error('Error adding watermark:', error);
                showStatus(`Error: ${error.message}`, 'error');
            } finally {
                watermarkBtn.disabled = false;
            }
        }

        async function addWatermarkWithCanvas(imageData) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    // Create canvas with same dimensions as original image
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    canvas.width = img.width;
                    canvas.height = img.height;

                    // Draw original image
                    ctx.drawImage(img, 0, 0);

                    // Add watermark
                    addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');

                    // Convert to data URL
                    const watermarkedData = canvas.toDataURL('image/png');
                    resolve(watermarkedData);
                };
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageData;
            });
        }

        function addWatermarkToCanvas(ctx, width, height, text) {
            // Save current state
            ctx.save();

            // Set watermark style with adaptive font size
            const fontSize = Math.max(width, height) * 0.025; // Slightly smaller for consistency
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // Fixed density approach - consistent spacing regardless of image size
            const baseSpacing = 300; // Increased base spacing for less density
            const spacingX = Math.max(baseSpacing, width * 0.25);  // 25% of width, minimum 300px
            const spacingY = Math.max(baseSpacing * 0.7, height * 0.2); // 20% of height, minimum 210px

            // Rotation angle (-45 degrees)
            const angle = -Math.PI / 4;

            // Calculate grid based on image dimensions, not diagonal
            const cols = Math.ceil(width / spacingX) + 3;
            const rows = Math.ceil(height / spacingY) + 3;

            // Calculate starting offset to center the pattern
            const startX = -(cols * spacingX - width) / 2;
            const startY = -(rows * spacingY - height) / 2;

            // Add watermarks in grid pattern
            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    ctx.save();

                    const x = startX + col * spacingX;
                    const y = startY + row * spacingY;

                    // Only draw if the watermark center is within reasonable bounds
                    if (x > -spacingX && x < width + spacingX &&
                        y > -spacingY && y < height + spacingY) {

                        ctx.translate(x, y);
                        ctx.rotate(angle);

                        ctx.strokeText(text, 0, 0);
                        ctx.fillText(text, 0, 0);
                    }

                    ctx.restore();
                }
            }

            // Restore state
            ctx.restore();
        }

        function showStatus(message, type) {
            status.innerHTML = message;
            status.className = `status ${type}`;
        }

        async function downloadWatermarkedImage() {
            if (!currentWatermarkedImageData) {
                showStatus('No watermarked image to download.', 'error');
                return;
            }

            try {
                // Convert SVG to PNG for download
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function() {
                    canvas.width = img.width || 800;
                    canvas.height = img.height || 600;
                    ctx.drawImage(img, 0, 0);

                    // Convert canvas to blob and download
                    canvas.toBlob(function(blob) {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'watermarked-image.png';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                        showStatus('Image downloaded successfully!', 'success');
                    }, 'image/png');
                };

                img.onerror = function() {
                    // Fallback: direct download of SVG
                    const a = document.createElement('a');
                    a.href = currentWatermarkedImageData;
                    a.download = 'watermarked-image.svg';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    showStatus('SVG image downloaded successfully!', 'success');
                };

                img.src = currentWatermarkedImageData;

            } catch (error) {
                console.error('Download failed:', error);
                showStatus('Download failed. Please try again.', 'error');
            }
        }
    </script>
</body>
</html>
