<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Complete Watermark Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .result {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .flow-step {
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .step-complete {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .step-error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>Test Complete Watermark Flow</h1>
    <p>This page tests the complete watermark flow to ensure database gets the correct URL.</p>
    
    <div class="test-section">
        <h2>Current Status</h2>
        <div id="statusInfo">
            <div class="flow-step" id="step1">Step 1: Check user credits</div>
            <div class="flow-step" id="step2">Step 2: Simulate image generation</div>
            <div class="flow-step" id="step3">Step 3: Process watermark (if needed)</div>
            <div class="flow-step" id="step4">Step 4: Upload to R2 and update database</div>
            <div class="flow-step" id="step5">Step 5: Verify final result</div>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="runCompleteTest()" id="testBtn">Run Complete Test</button>
        <button onclick="clearResults()" id="clearBtn">Clear Results</button>
        <input type="file" id="fileInput" accept="image/*" style="margin: 10px 0;">
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>Expected Database State</h2>
        <div id="expectedState" class="result warning">
            <strong>For users with ≤5 credits:</strong>
            generated_image_url should contain: https://img.kontext-dev.com/watermarked-kontextdev_xxx-0-timestamp-uuid.png

            <strong>For users with >5 credits:</strong>
            generated_image_url should contain: https://img.kontext-dev.com/kontext-dev-com-uuid.png (original image)
        </div>
    </div>

    <script>
        let testTaskId = '';
        let userCredits = null;

        function addResult(title, content, type = 'success') {
            const container = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}] ${title}</strong>\n${content}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function updateStep(stepId, status, message = '') {
            const step = document.getElementById(stepId);
            step.className = `flow-step ${status === 'complete' ? 'step-complete' : status === 'error' ? 'step-error' : ''}`;
            if (message) {
                step.textContent = step.textContent.split(':')[0] + ': ' + message;
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            // Reset steps
            for (let i = 1; i <= 5; i++) {
                const step = document.getElementById(`step${i}`);
                step.className = 'flow-step';
                step.textContent = step.textContent.split(':')[0] + ':';
            }
        }

        async function runCompleteTest() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            testTaskId = `test_complete_${Date.now()}`;

            try {
                addResult('Test Started', `Task ID: ${testTaskId}`);

                // Step 1: Check credits
                updateStep('step1', 'running', 'Checking user credits...');
                await checkUserCredits();

                // Step 2: Simulate generation
                updateStep('step2', 'running', 'Simulating image generation...');
                await simulateImageGeneration();

                // Step 3: Process watermark
                updateStep('step3', 'running', 'Processing watermark...');
                await processWatermark();

                // Step 4: Upload and update database
                updateStep('step4', 'running', 'Uploading to R2 and updating database...');
                await uploadAndUpdateDatabase();

                // Step 5: Verify result
                updateStep('step5', 'running', 'Verifying final result...');
                await verifyResult();

                addResult('Test Completed', 'All steps completed successfully!', 'success');

            } catch (error) {
                addResult('Test Failed', error.message, 'error');
            } finally {
                btn.disabled = false;
            }
        }

        async function checkUserCredits() {
            try {
                const response = await fetch('/api/credits');
                if (response.ok) {
                    const data = await response.json();
                    userCredits = data.left_credits;
                    
                    updateStep('step1', 'complete', `Credits: ${userCredits} (${userCredits <= 5 ? 'Watermark needed' : 'No watermark'})`);
                    addResult('Credits Check', 
                        `User credits: ${userCredits}\n` +
                        `Pro status: ${data.is_pro}\n` +
                        `Watermark required: ${userCredits <= 5 ? 'YES' : 'NO'}`
                    );
                } else {
                    throw new Error('Failed to fetch credits');
                }
            } catch (error) {
                updateStep('step1', 'error', 'Failed to check credits');
                throw error;
            }
        }

        async function simulateImageGeneration() {
            // This simulates what happens in the backend
            const originalImageUrl = `https://img.kontext-dev.com/kontext-dev-com-${Date.now()}.png`;
            
            updateStep('step2', 'complete', 'Image generated (simulated)');
            addResult('Image Generation', 
                `Original image URL: ${originalImageUrl}\n` +
                `Backend behavior: ${userCredits <= 5 ? 'Skip database update' : 'Update database with original URL'}`
            );
        }

        async function processWatermark() {
            if (userCredits > 5) {
                updateStep('step3', 'complete', 'Skipped (user has sufficient credits)');
                addResult('Watermark Processing', 'Skipped - user has sufficient credits');
                return;
            }

            const file = document.getElementById('fileInput').files[0];
            if (!file) {
                throw new Error('Please select an image file for watermark testing');
            }

            try {
                // Convert to base64
                const reader = new FileReader();
                const base64Promise = new Promise((resolve) => {
                    reader.onload = () => resolve(reader.result);
                });
                reader.readAsDataURL(file);
                const imageData = await base64Promise;

                // Add watermark
                const watermarkedData = await addWatermarkToImage(imageData);
                
                updateStep('step3', 'complete', 'Watermark added successfully');
                addResult('Watermark Processing', 
                    `Original image size: ${imageData.length} characters\n` +
                    `Watermarked image size: ${watermarkedData.length} characters\n` +
                    `Watermark added successfully`
                );

                // Store for next step
                window.testWatermarkedData = watermarkedData;

            } catch (error) {
                updateStep('step3', 'error', 'Failed to process watermark');
                throw error;
            }
        }

        async function uploadAndUpdateDatabase() {
            if (userCredits > 5) {
                updateStep('step4', 'complete', 'Skipped (no watermark needed)');
                addResult('Database Update', 'Skipped - backend already updated database with original image');
                return;
            }

            if (!window.testWatermarkedData) {
                throw new Error('No watermarked data available');
            }

            try {
                const uploadResponse = await fetch('/api/upload-watermarked-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        imageData: window.testWatermarkedData,
                        taskId: testTaskId,
                        originalUrl: 'test-original-url',
                        imageIndex: 0
                    }),
                });

                if (uploadResponse.ok) {
                    const result = await uploadResponse.json();
                    updateStep('step4', 'complete', 'Uploaded and database updated');
                    addResult('Database Update', 
                        `✅ Watermarked image uploaded to R2\n` +
                        `✅ Database updated with watermarked URL\n` +
                        `✅ Final URL: ${result.watermarkedUrl}\n` +
                        `✅ Task ID: ${testTaskId}`
                    );
                    
                    window.testFinalUrl = result.watermarkedUrl;
                } else {
                    const error = await uploadResponse.text();
                    throw new Error(`Upload failed: ${error}`);
                }
            } catch (error) {
                updateStep('step4', 'error', 'Failed to upload or update database');
                throw error;
            }
        }

        async function verifyResult() {
            updateStep('step5', 'complete', 'Verification completed');
            
            if (userCredits <= 5) {
                addResult('Final Verification', 
                    `✅ User had ≤5 credits\n` +
                    `✅ Watermark was processed\n` +
                    `✅ Watermarked image uploaded to R2\n` +
                    `✅ Database updated with watermarked URL\n` +
                    `✅ Final URL: ${window.testFinalUrl || 'N/A'}\n` +
                    `\n📋 Next: Check your database to verify generated_image_url contains the watermarked URL`,
                    'success'
                );
            } else {
                addResult('Final Verification', 
                    `✅ User had >5 credits\n` +
                    `✅ No watermark processing needed\n` +
                    `✅ Backend updated database with original image URL\n` +
                    `\n📋 Next: Check your database to verify generated_image_url contains the original URL`,
                    'success'
                );
            }
        }

        // Watermark function
        async function addWatermarkToImage(imageData) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    ctx.drawImage(img, 0, 0);
                    addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'kontext-dev.com');
                    
                    const watermarkedData = canvas.toDataURL('image/png');
                    resolve(watermarkedData);
                };
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageData;
            });
        }

        function addWatermarkToCanvas(ctx, width, height, text) {
            ctx.save();
            const fontSize = Math.max(width, height) * 0.025;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const baseSpacing = 300;
            const spacingX = Math.max(baseSpacing, width * 0.25);
            const spacingY = Math.max(baseSpacing * 0.7, height * 0.2);
            const angle = -Math.PI / 4;
            const cols = Math.ceil(width / spacingX) + 3;
            const rows = Math.ceil(height / spacingY) + 3;
            const startX = -(cols * spacingX - width) / 2;
            const startY = -(rows * spacingY - height) / 2;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    ctx.save();
                    const x = startX + col * spacingX;
                    const y = startY + row * spacingY;
                    if (x > -spacingX && x < width + spacingX && y > -spacingY && y < height + spacingY) {
                        ctx.translate(x, y);
                        ctx.rotate(angle);
                        ctx.strokeText(text, 0, 0);
                        ctx.fillText(text, 0, 0);
                    }
                    ctx.restore();
                }
            }
            ctx.restore();
        }
    </script>
</body>
</html>
